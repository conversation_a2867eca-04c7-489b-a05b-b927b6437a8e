import type { IReqSettingScheduleRegularWorkUnit } from '@/types/request/IReqSettingScheduleRegularWorkUnit.ts';

export const mockDataCreateWorkUnitRequest: IReqSettingScheduleRegularWorkUnit = {
  timetable: [],
  day: [
    {
      index: 1,
      label: 'Senin',
      data: [
        { name: '<PERSON> Masuk', start_time: '07:00:00', end_time: '07:30:00' },
        { name: '<PERSON>', start_time: '17:00:00', end_time: '17:30:00' },
      ],
    },
    {
      index: 2,
      label: '<PERSON><PERSON><PERSON>',
      data: [
        { name: '<PERSON>', start_time: '07:00:00', end_time: '07:30:00' },
        { name: '<PERSON>', start_time: '17:00:00', end_time: '17:30:00' },
      ],
    },
    {
      index: 3,
      label: '<PERSON><PERSON>',
      data: [
        { name: '<PERSON>', start_time: '07:00:00', end_time: '07:30:00' },
        { name: '<PERSON>', start_time: '17:00:00', end_time: '17:30:00' },
      ],
    },
    {
      index: 4,
      label: '<PERSON><PERSON>',
      data: [
        { name: '<PERSON> <PERSON><PERSON>k', start_time: '07:00:00', end_time: '07:30:00' },
        { name: 'Jam Keluar', start_time: '17:00:00', end_time: '17:30:00' },
      ],
    },
    {
      index: 5,
      label: '<PERSON><PERSON>',
      data: [
        { name: 'Jam Masuk', start_time: '08:00:00', end_time: '08:30:00' },
        { name: 'Jam Keluar', start_time: '14:00:00', end_time: '14:30:00' },
      ],
    },
    {
      index: 6,
      label: 'Sabtu',
      data: [
        { name: 'Jam Masuk', start_time: '08:00:00', end_time: '08:30:00' },
        { name: 'Jam Keluar', start_time: '14:00:00', end_time: '14:30:00' },
      ],
    },
    {
      index: 7,
      label: 'Minggu',
      is_holiday: true,
      data: [{ name: 'Jam Masuk', start_time: '03:00:00', end_time: '12:00' }],
    },
  ],
};
