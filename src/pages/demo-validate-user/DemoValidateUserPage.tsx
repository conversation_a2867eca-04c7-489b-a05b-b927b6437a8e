import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useUi } from '@/hooks/useUi';
import ErrorService from '@/services/error.service';
import { HttpService } from '@/services/http.service';
import * as faceapi from 'face-api.js';
import { Check, Loader2, RefreshCw, X } from 'lucide-react';
import { useEffect, useRef, useState } from 'react';
import Webcam from 'react-webcam';

export default function DemoValidateUser() {
  const webcamRef = useRef<Webcam>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [modelsLoaded, setModelsLoaded] = useState(false);
  const [faceDetected, setFaceDetected] = useState(false);
  const [consecutiveFrames, setConsecutiveFrames] = useState(0);
  const [isCapturing, setIsCapturing] = useState(true);
  const [isProcessing, setIsProcessing] = useState(false);
  const [validationResult, setValidationResult] = useState<{
    success: boolean;
    message: string;
    employeeName?: string;
  } | null>(null);
  const [loadingProgress, setLoadingProgress] = useState(0);

  const httpService = new HttpService();
  const errorService = new ErrorService();
  const { toast } = useUi();

  // Load face detection models
  useEffect(() => {
    const loadModels = async () => {
      try {
        const MODEL_URL = '/models';

        // Load face detection model
        await faceapi.nets.tinyFaceDetector.loadFromUri(MODEL_URL);
        setLoadingProgress(50);

        // Load face landmark model
        await faceapi.nets.faceLandmark68Net.loadFromUri(MODEL_URL);
        setLoadingProgress(100);

        setModelsLoaded(true);
        console.info('Face detection models loaded successfully');
      } catch (error) {
        console.error('Error loading face detection models:', error);
        toast.error('Gagal memuat model deteksi wajah');
      }
    };

    loadModels();
  }, []);

  // Face detection loop
  useEffect(() => {
    if (!modelsLoaded || !isCapturing) return;

    const detectionInterval = setInterval(async () => {
      const video = webcamRef.current?.video;
      const canvas = canvasRef.current;

      if (video && video.readyState === 4 && canvas) {
        try {
          // Detect faces
          const detections = await faceapi
            .detectAllFaces(video, new faceapi.TinyFaceDetectorOptions())
            .withFaceLandmarks();

          // Clear canvas and draw detections
          const context = canvas.getContext('2d');
          if (context) {
            context.clearRect(0, 0, canvas.width, canvas.height);

            // Resize detections to match canvas size
            const displaySize = {
              width: video.videoWidth,
              height: video.videoHeight,
            };
            faceapi.matchDimensions(canvas, displaySize);
            const resizedDetections = faceapi.resizeResults(detections, displaySize);

            // Draw detections
            faceapi.draw.drawDetections(canvas, resizedDetections);
            faceapi.draw.drawFaceLandmarks(canvas, resizedDetections);

            // Update face detection status
            const faceFound = detections.length === 1; // Only detect one face
            setFaceDetected(faceFound);

            // Handle consecutive frames logic
            if (faceFound) {
              setConsecutiveFrames((prev) => {
                const newCount = prev + 1;
                // If we've detected a face for 1 second (assuming 30fps, so ~30 frames)
                if (newCount >= 30 && !isProcessing) {
                  captureAndValidate();
                }
                return newCount;
              });
            } else {
              setConsecutiveFrames(0);
            }
          }
        } catch (error) {
          console.error('Error during face detection:', error);
        }
      }
    }, 33); // ~30fps

    return () => clearInterval(detectionInterval);
  }, [modelsLoaded, isCapturing, isProcessing]);

  // Function to capture image and send for validation
  const captureAndValidate = async () => {
    if (!webcamRef.current || isProcessing) return;

    try {
      setIsProcessing(true);

      // Capture image
      const imageSrc = webcamRef.current.getScreenshot();
      if (!imageSrc) {
        toast.error('Gagal mengambil gambar');
        setIsProcessing(false);
        return;
      }

      // Convert base64 to blob
      const base64Data = imageSrc.split(',')[1];
      const blob = await fetch(`data:image/jpeg;base64,${base64Data}`).then((res) => res.blob());

      // Create form data
      const formData = new FormData();
      formData.append('image', blob, 'face.jpg');

      // Send to API
      const response = await httpService.FormData('/employee/v1/validate', formData);

      // Handle response
      if (response.status === 200) {
        setValidationResult({
          success: true,
          message: 'Validasi berhasil',
          employeeName: response.data?.response_data?.name || 'Pengguna',
        });
        toast.success('Validasi wajah berhasil');
      } else {
        setValidationResult({
          success: false,
          message: 'Validasi gagal',
        });
        toast.error('Validasi wajah gagal');
      }
    } catch (error) {
      console.error('Error validating face:', error);
      errorService.fetchApiError(error);
      setValidationResult({
        success: false,
        message: 'Terjadi kesalahan saat validasi',
      });
    } finally {
      setIsProcessing(false);
      setIsCapturing(false);
    }
  };

  // Reset validation
  const resetValidation = () => {
    setValidationResult(null);
    setConsecutiveFrames(0);
    setIsCapturing(true);
  };

  return (
    <div className="w-full max-w-4xl mx-auto">
      {/* Header Section */}
      <div className="text-center mb-6">
        <div className="flex items-center justify-center gap-3 mb-4">
          <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
            <Check className="w-6 h-6 text-blue-600" />
          </div>
          <h2 className="text-2xl font-bold text-gray-900">Sistem Validasi Wajah</h2>
        </div>
        <p className="text-gray-600 max-w-2xl mx-auto">
          Teknologi pengenalan wajah canggih untuk sistem absensi yang akurat dan aman. Posisikan wajah Anda di tengah
          kamera untuk memulai validasi.
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Camera Section */}
        <div className="lg:col-span-2">
          <Card className="overflow-hidden">
            <CardHeader className="pb-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                    <Loader2 className={`w-4 h-4 text-blue-600 ${!modelsLoaded ? 'animate-spin' : ''}`} />
                  </div>
                  <div>
                    <CardTitle className="text-lg">Kamera Validasi</CardTitle>
                    <p className="text-sm text-gray-600">
                      {!modelsLoaded ? 'Memuat model AI...' : isCapturing ? 'Siap untuk validasi' : 'Validasi selesai'}
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  {modelsLoaded && (
                    <div className="flex items-center gap-1 text-xs text-green-600">
                      <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                      <span>AI Ready</span>
                    </div>
                  )}
                </div>
              </div>
            </CardHeader>
            <CardContent className="p-0">
              <div className="relative aspect-video bg-gray-900 overflow-hidden">
                {/* Webcam */}
                <Webcam
                  ref={webcamRef}
                  audio={false}
                  screenshotFormat="image/jpeg"
                  videoConstraints={{
                    facingMode: 'user',
                    width: { ideal: 1280 },
                    height: { ideal: 720 },
                  }}
                  className="w-full h-full object-cover"
                  mirrored={true}
                />

                {/* Canvas for face detection */}
                <canvas ref={canvasRef} className="absolute top-0 left-0 w-full h-full" />

                {/* Loading overlay */}
                {!modelsLoaded && (
                  <div className="absolute inset-0 flex items-center justify-center bg-black/80 backdrop-blur-sm z-20">
                    <div className="text-white text-center max-w-sm">
                      <div className="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
                        <Loader2 className="w-8 h-8 animate-spin" />
                      </div>
                      <h3 className="text-lg font-semibold mb-3">Memuat Model AI</h3>
                      <div className="w-full bg-gray-700 rounded-full h-2 mb-3">
                        <div
                          className="h-full bg-blue-500 rounded-full transition-all duration-300"
                          style={{ width: `${loadingProgress}%` }}
                        ></div>
                      </div>
                      <p className="text-sm text-gray-300">
                        Memuat model deteksi wajah... {loadingProgress.toFixed(0)}%
                      </p>
                    </div>
                  </div>
                )}

                {/* Processing overlay */}
                {isProcessing && (
                  <div className="absolute inset-0 flex items-center justify-center bg-black/80 backdrop-blur-sm z-20">
                    <div className="text-white text-center">
                      <div className="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
                        <Loader2 className="w-8 h-8 animate-spin" />
                      </div>
                      <h3 className="text-lg font-semibold mb-2">Memproses Validasi</h3>
                      <p className="text-sm text-gray-300">Menganalisis wajah Anda...</p>
                    </div>
                  </div>
                )}

                {/* Face detection indicator */}
                {faceDetected && !isProcessing && modelsLoaded && (
                  <div className="absolute top-4 right-4 z-20">
                    <div className="bg-green-500/90 backdrop-blur-sm text-white px-3 py-2 rounded-lg text-sm font-medium">
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
                        <span>Wajah Terdeteksi</span>
                      </div>
                      {consecutiveFrames >= 15 && (
                        <div className="mt-1 text-xs">
                          Validasi dalam {Math.ceil((30 - consecutiveFrames) / 10)} detik
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {/* Validation result overlay */}
                {validationResult && (
                  <div className="absolute inset-0 flex items-center justify-center bg-black/90 backdrop-blur-sm z-30">
                    <div className="bg-white rounded-xl p-8 max-w-sm w-full mx-4 text-center">
                      {validationResult.success ? (
                        <>
                          <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
                            <Check className="w-10 h-10 text-green-600" />
                          </div>
                          <h3 className="text-2xl font-bold text-gray-900 mb-3">Validasi Berhasil!</h3>
                          <p className="text-gray-600 mb-6">
                            Selamat datang,{' '}
                            <span className="font-semibold text-green-600">{validationResult.employeeName}</span>!
                          </p>
                          <div className="bg-green-50 rounded-lg p-4 mb-6">
                            <p className="text-sm text-green-700">
                              ✓ Identitas terverifikasi
                              <br />
                              ✓ Akses sistem diizinkan
                              <br />✓ Waktu: {new Date().toLocaleTimeString('id-ID')}
                            </p>
                          </div>
                        </>
                      ) : (
                        <>
                          <div className="w-20 h-20 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6">
                            <X className="w-10 h-10 text-red-600" />
                          </div>
                          <h3 className="text-2xl font-bold text-gray-900 mb-3">Validasi Gagal</h3>
                          <p className="text-gray-600 mb-6">
                            Wajah tidak dikenali dalam database. Silakan coba lagi atau hubungi administrator.
                          </p>
                          <div className="bg-red-50 rounded-lg p-4 mb-6">
                            <p className="text-sm text-red-700">
                              ✗ Wajah tidak terdaftar
                              <br />
                              ✗ Pastikan pencahayaan cukup
                              <br />✗ Posisikan wajah di tengah kamera
                            </p>
                          </div>
                        </>
                      )}
                      <Button onClick={resetValidation} className="w-full" size="lg">
                        <RefreshCw className="w-4 h-4 mr-2" />
                        Coba Lagi
                      </Button>
                    </div>
                  </div>
                )}

                {/* Camera guide overlay */}
                {modelsLoaded && !faceDetected && !isProcessing && !validationResult && (
                  <div className="absolute inset-0 flex items-center justify-center pointer-events-none z-10">
                    <div className="border-2 border-white/50 rounded-full w-64 h-64 flex items-center justify-center">
                      <div className="border-2 border-white/30 rounded-full w-48 h-48 flex items-center justify-center">
                        <div className="text-white/80 text-center">
                          <div className="w-8 h-8 border-2 border-white/60 rounded-full mx-auto mb-2"></div>
                          <p className="text-sm">Posisikan wajah di sini</p>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Info Panel */}
        <div className="space-y-6">
          {/* Status Card */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Status Sistem</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center gap-3">
                <div className={`w-3 h-3 rounded-full ${modelsLoaded ? 'bg-green-500' : 'bg-yellow-500'}`}></div>
                <div>
                  <p className="text-sm font-medium">Model AI</p>
                  <p className="text-xs text-gray-600">{modelsLoaded ? 'Siap' : 'Memuat...'}</p>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <div className={`w-3 h-3 rounded-full ${faceDetected ? 'bg-green-500' : 'bg-gray-300'}`}></div>
                <div>
                  <p className="text-sm font-medium">Deteksi Wajah</p>
                  <p className="text-xs text-gray-600">{faceDetected ? 'Terdeteksi' : 'Tidak terdeteksi'}</p>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <div className={`w-3 h-3 rounded-full ${isCapturing ? 'bg-blue-500' : 'bg-gray-300'}`}></div>
                <div>
                  <p className="text-sm font-medium">Kamera</p>
                  <p className="text-xs text-gray-600">{isCapturing ? 'Aktif' : 'Tidak aktif'}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Instructions Card */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Petunjuk Penggunaan</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3 text-sm">
                <div className="flex items-start gap-3">
                  <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                    <span className="text-xs font-bold text-blue-600">1</span>
                  </div>
                  <p className="text-gray-700">Pastikan pencahayaan ruangan cukup terang</p>
                </div>
                <div className="flex items-start gap-3">
                  <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                    <span className="text-xs font-bold text-blue-600">2</span>
                  </div>
                  <p className="text-gray-700">Posisikan wajah di tengah area kamera</p>
                </div>
                <div className="flex items-start gap-3">
                  <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                    <span className="text-xs font-bold text-blue-600">3</span>
                  </div>
                  <p className="text-gray-700">Tahan posisi selama 1 detik untuk validasi otomatis</p>
                </div>
                <div className="flex items-start gap-3">
                  <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                    <span className="text-xs font-bold text-blue-600">4</span>
                  </div>
                  <p className="text-gray-700">Tunggu hasil validasi dari sistem</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Reset Button */}
          {!isCapturing && !validationResult && (
            <Button onClick={resetValidation} className="w-full" variant="outline">
              <RefreshCw className="w-4 h-4 mr-2" />
              Mulai Ulang Validasi
            </Button>
          )}
        </div>
      </div>

      {/* Footer Info */}
      <div className="mt-8 text-center">
        <div className="bg-blue-50 rounded-lg p-4 border border-blue-200">
          <p className="text-sm text-blue-700">
            <strong>Teknologi AI:</strong> Sistem menggunakan model deep learning untuk pengenalan wajah dengan tingkat
            akurasi tinggi dan keamanan data terjamin.
          </p>
        </div>
      </div>
    </div>
  );
}
