import { AgencyRepository } from '@/repositories/agency-repository';
import { EmployeeRepository } from '@/repositories/employee-repostiory';
import type { ILabelValue } from '@/types/type/ILabelValue';
import { useQuery } from '@tanstack/react-query';
import { useEffect, useState } from 'react';
import { useSearchParams } from 'react-router-dom';

export interface EmployeeFilter {
  agency_id: string;
  q: string;
  page: number;
  size: number;
}

export function useListEmployeePage() {
  const employeeRepository = new EmployeeRepository();
  const agencyRepository = new AgencyRepository();

  const [searchParams, setSearchParams] = useSearchParams();
  const getInitialFilter = (): EmployeeFilter => {
    return {
      agency_id: searchParams.get('agency_id') || '',
      q: searchParams.get('q') || '',
      page: parseInt(searchParams.get('page') || '0'),
      size: parseInt(searchParams.get('size') || '10'),
    };
  };

  const [openFilter, setOpenFilter] = useState<boolean>(false);
  const [filterData, setFilterData] = useState<EmployeeFilter>(getInitialFilter());
  const [totalItems, setTotalItems] = useState<number>(0);
  const [totalPages, setTotalPages] = useState<number>(0);
  const [searchValue, setSearchValue] = useState(filterData.q);

  useEffect(() => {
    const params = new URLSearchParams();
    if (filterData.agency_id) params.set('agency_id', filterData.agency_id);
    if (filterData.q) params.set('q', filterData.q);
    params.set('page', filterData.page.toString());
    params.set('size', filterData.size.toString());
    setSearchParams(params);
  }, [filterData, setSearchParams]);

  const queryListAgency = useQuery({
    queryKey: ['list_agency_page_and_filter'],
    queryFn: agencyRepository.listAgency,
  });

  const queryList = useQuery({
    queryKey: ['list_employe_admin', filterData],
    queryFn: () => employeeRepository.listEmployee(filterData, setTotalPages, setTotalItems),
  });

  function submitFilter() {
    setOpenFilter(false);
    queryList.refetch();
  }

  function handleResetFilter() {
    setSearchValue('');
    setOpenFilter(false);
    setFilterData({
      agency_id: '',
      q: '',
      page: 0,
      size: 10,
    });
  }

  function handleSearch() {
    const searchText = searchValue;
    if (searchText) {
      setFilterData((prev) => ({
        ...prev,
        q: searchText,
        page: 0, // Reset to first page on new search
      }));
    }
  }

  function handlePaginationChange(params: { page?: number; size?: number }) {
    setFilterData((prev) => ({
      ...prev,
      page: params.size !== undefined && params.size !== prev.size ? 0 : (params.page ?? prev.page),
      size: params.size ?? prev.size,
    }));
  }
  function handleResetSearch() {
    setSearchValue('');
    setFilterData((prev) => ({
      ...prev,
      q: '',
      page: 0, // Reset to first page on new search
    }));
  }

  const dataList = queryList.data || [];
  const dataAgencyFilter: ILabelValue<string>[] = (queryListAgency.data || []).map((e) => {
    return {
      label: e.name,
      value: e.id,
    };
  });
  const loading = queryList.isPending;

  function isActiveSearch() {
    return !!filterData.q.length;
  }

  return {
    dataList,
    openFilter,
    setOpenFilter,
    dataAgencyFilter,
    filterData,
    setFilterData,
    loading,
    handleResetSearch,
    submitFilter,
    handleResetFilter,
    handleSearch,
    handlePaginationChange,
    isActiveSearch,
    searchValue,
    setSearchValue,
    pagination: {
      page: filterData.page,
      size: filterData.size,
      total_data: totalItems,
      page_count: totalPages,
    },
  };
}
