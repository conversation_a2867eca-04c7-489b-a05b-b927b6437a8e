import type { FaceAnalysisResult } from '@/components/RegisterCamCard';
import { ENDPOINT } from '@/constants/endpoint';
import { useUi } from '@/hooks/useUi';
import { ROUTES } from '@/routes/routes';
import ErrorService from '@/services/error.service';
import { HttpService } from '@/services/http.service';
import type { IResDetailEmployee } from '@/types/response/IResDetailEmployee';
import type { BaseResponse } from '@/types/response/IResModel';
import { useMutation, useQuery } from '@tanstack/react-query';
import { useEffect, useRef, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import Webcam from 'react-webcam';

type FaceInstruction = {
  id: number;
  instruction: string;
  completed: boolean;
  detectionCriteria: keyof FaceAnalysisResult;
  requiredValue: boolean;
  consecutiveFramesRequired: number;
};

export function useRegisterEmployeeFacePage() {
  const httpService = new HttpService();
  const errorService = new ErrorService();
  const { toast } = useUi();
  const navigate = useNavigate();
  const { id } = useParams();
  const webcamRef = useRef<Webcam>(null);
  const [capturedImages, setCapturedImages] = useState<string[]>([]);
  const [currentInstructionIndex, setCurrentInstructionIndex] = useState(0);
  const [autoCapture, setAutoCapture] = useState(true);
  const [faceAnalysis, setFaceAnalysis] = useState<FaceAnalysisResult | null>(null);
  const [consecutiveFrames, setConsecutiveFrames] = useState(0);
  const [captureReady, setCaptureReady] = useState(false);
  const [captureCountdown, setCaptureCountdown] = useState(0);
  const [countdownIntervalId, setCountdownIntervalId] = useState<number | null>(null);

  // Face capture instructions with detection criteria
  const [faceInstructions, setFaceInstructions] = useState<FaceInstruction[]>([
    {
      id: 1,
      instruction: 'Wajah menghadap ke depan',
      completed: false,
      detectionCriteria: 'lookingCenter',
      requiredValue: true,
      consecutiveFramesRequired: 20,
    },
    {
      id: 2,
      instruction: 'Menengok ke kanan',
      completed: false,
      detectionCriteria: 'lookingRight',
      requiredValue: true,
      consecutiveFramesRequired: 15,
    },
    {
      id: 3,
      instruction: 'Menengok ke kiri',
      completed: false,
      detectionCriteria: 'lookingLeft',
      requiredValue: true,
      consecutiveFramesRequired: 15,
    },
    {
      id: 4,
      instruction: 'Buka mulut',
      completed: false,
      detectionCriteria: 'mouthOpen',
      requiredValue: true,
      consecutiveFramesRequired: 15,
    },
    {
      id: 5,
      instruction: 'Perlihatkan gigi (tersenyum lebar)',
      completed: false,
      detectionCriteria: 'showingTeeth',
      requiredValue: true,
      consecutiveFramesRequired: 15,
    },
  ]);

  const queryDetail = useQuery({
    queryKey: ['detailEmployee', id],
    enabled: !!id,
    queryFn: async () => {
      try {
        const response: BaseResponse<IResDetailEmployee> = await httpService.GET(ENDPOINT.DETAIL_EMPLOYEE(id!));
        return response.data.response_data;
      } catch (error) {
        errorService.fetchApiError(error);
      }
    },
  });

  // Clean up interval on unmount
  useEffect(() => {
    return () => {
      if (countdownIntervalId) {
        clearInterval(countdownIntervalId);
      }
    };
  }, [countdownIntervalId]);

  // Handle face analysis results from RegisterCamCard
  const handleFaceAnalysis = (analysis: FaceAnalysisResult) => {
    setFaceAnalysis(analysis);

    if (!autoCapture || currentInstructionIndex >= faceInstructions.length) return;

    const currentInstruction = faceInstructions[currentInstructionIndex];

    // Check if the current detection criteria is met
    const criteriaValue = analysis[currentInstruction.detectionCriteria];
    const criteriaMatched = criteriaValue === currentInstruction.requiredValue;

    if (analysis.faceDetected && criteriaMatched) {
      // Increment consecutive frames counter
      setConsecutiveFrames((prev) => prev + 1);

      // If we've reached the required number of consecutive frames
      if (consecutiveFrames >= currentInstruction.consecutiveFramesRequired && !captureReady) {
        setCaptureReady(true);
        setCaptureCountdown(3);

        // Start countdown for capture
        const intervalId = window.setInterval(() => {
          setCaptureCountdown((prev) => {
            if (prev <= 1) {
              clearInterval(intervalId);
              setCountdownIntervalId(null);
              // Trigger capture after countdown
              setTimeout(() => {
                captureManually();
                setCaptureReady(false);
              }, 300);
              return 0;
            }
            return prev - 1;
          });
        }, 1000);

        setCountdownIntervalId(intervalId);
      }
    } else {
      // Reset consecutive frames if criteria not met
      setConsecutiveFrames(0);
      setCaptureReady(false);
      setCaptureCountdown(0);

      // Clear any existing countdown
      if (countdownIntervalId) {
        clearInterval(countdownIntervalId);
        setCountdownIntervalId(null);
      }
    }
  };

  // Fungsi untuk menangani gambar yang diambil dari RegisterCamCard
  const handleCapturedImage = (imageData: string) => {
    // Tambahkan gambar ke array capturedImages
    setCapturedImages((prev) => [...prev, imageData]);
    const updatedInstructions = [...faceInstructions];
    updatedInstructions[currentInstructionIndex].completed = true;
    setFaceInstructions(updatedInstructions);

    setConsecutiveFrames(0);
    setCaptureReady(false);
    setCaptureCountdown(0);

    // Clear any existing countdown
    if (countdownIntervalId) {
      clearInterval(countdownIntervalId);
      setCountdownIntervalId(null);
    }

    // Pindah ke instruksi berikutnya jika tersedia
    if (currentInstructionIndex < faceInstructions.length - 1) {
      setCurrentInstructionIndex((prev) => prev + 1);
    }

    toast.success(`Foto ${currentInstructionIndex + 1} berhasil diambil`);
  };

  // Fungsi untuk mengambil gambar secara manual
  const captureManually = () => {
    if (webcamRef.current) {
      const imageSrc = webcamRef.current.getScreenshot();
      if (imageSrc) {
        handleCapturedImage(imageSrc);
      }
    }
  };

  const resetCapture = () => {
    setCapturedImages([]);
    setCurrentInstructionIndex(0);
    setConsecutiveFrames(0);
    setCaptureReady(false);
    setCaptureCountdown(0);

    // Clear any existing countdown
    if (countdownIntervalId) {
      clearInterval(countdownIntervalId);
      setCountdownIntervalId(null);
    }

    setFaceInstructions(faceInstructions.map((instr) => ({ ...instr, completed: false })));
  };

  const toggleAutoCapture = () => {
    setAutoCapture((prev) => !prev);
    toast.success(autoCapture ? 'Mode pengambilan manual diaktifkan' : 'Mode pengambilan otomatis diaktifkan');

    // Reset state when toggling
    setConsecutiveFrames(0);
    setCaptureReady(false);
    setCaptureCountdown(0);

    // Clear any existing countdown
    if (countdownIntervalId) {
      clearInterval(countdownIntervalId);
      setCountdownIntervalId(null);
    }
  };

  // Convert base64 to Blob
  const base64ToBlob = (base64: string) => {
    const parts = base64.split(';base64,');
    const contentType = parts[0].split(':')[1];
    const raw = window.atob(parts[1]);
    const rawLength = raw.length;
    const uInt8Array = new Uint8Array(rawLength);

    for (let i = 0; i < rawLength; ++i) {
      uInt8Array[i] = raw.charCodeAt(i);
    }

    return new Blob([uInt8Array], { type: contentType });
  };

  const registerFaceMutation = useMutation({
    mutationFn: async () => {
      if (capturedImages.length < faceInstructions.length) {
        throw new Error(`Minimal ${faceInstructions.length} foto wajah diperlukan`);
      }

      const formData = new FormData();

      capturedImages.forEach((base64Image, index) => {
        const blob = base64ToBlob(base64Image);
        formData.append(`images`, blob, `face_${index + 1}.jpg`);
      });

      formData.append('employee_id', id || '');

      try {
        await new Promise((resolve) => setTimeout(resolve, 2000));
        await httpService.FormData(ENDPOINT.REGISTER_EMPLOYEE_FACE(id!), formData);
        toast.success('Wajah pegawai berhasil didaftarkan');
        navigate(ROUTES.DETAIL_EMPLOYEE(id!));
      } catch (error) {
        errorService.fetchApiError(error);
        throw error;
      }
    },
  });

  return {
    queryDetail,
    webcamRef,
    capturedImages,
    faceInstructions,
    currentInstructionIndex,
    resetCapture,
    handleCapturedImage,
    captureManually,
    registerFaceMutation,
    handleFaceAnalysis,
    autoCapture,
    toggleAutoCapture,
    captureReady,
    captureCountdown,
    faceAnalysis,
  };
}
