import PageContainer from '@/components/PageContainer.tsx';
import PageTitle from '@/components/page-title.tsx';
import type { IBreadcrumbData } from '@/types/type/IBreadcrumbData.ts';
import { ROUTES } from '@/routes/routes.ts';
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card.tsx';
import { Separator } from '@/components/ui/separator.tsx';
import MapsSearch from '@/components/MapsSearch.tsx';
import { FormikProvider } from 'formik';
import { useAgencyFormPage } from '@/pages/agency/form/useAgencyFormPage';
import InputText from '@/components/InputText.tsx';
import InputTextArea from '@/components/InputTextArea.tsx';
import { Checkbox } from '@/components/ui/checkbox.tsx';
import { Button } from '@/components/ui/button.tsx';
import { Building2, MapPin, CheckCircle, AlertCircle, ArrowLeft, Save, Edit3 } from 'lucide-react';
import { Link } from 'react-router-dom';
import AgencyFormSkeleton from './AgencyFormSkeleton.tsx';

export default function AgencyFormPage() {
  const page = useAgencyFormPage();
  const isEdit = !!page?.id;

  const breadcrumb: IBreadcrumbData[] = [
    {
      label: 'Master Data',
      path: ROUTES.MASTER_DATA.AGENCY(),
    },
    {
      label: 'Instansi',
      path: ROUTES.MASTER_DATA.AGENCY(),
    },
    {
      label: isEdit ? 'Edit Instansi' : 'Tambah Instansi',
    },
  ];

  // Show loading skeleton if editing and data not loaded yet
  if (isEdit && !page.formik.values.name) {
    return (
      <PageContainer className="max-w-4xl mx-auto">
        <PageTitle title={isEdit ? 'Edit Instansi' : 'Tambah Instansi'} breadcrumb={breadcrumb} />
        <AgencyFormSkeleton />
      </PageContainer>
    );
  }

  return (
    <PageContainer className="max-w-4xl mx-auto">
      {/* Header Section */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6">
        <PageTitle title={isEdit ? 'Edit Instansi' : 'Tambah Instansi'} breadcrumb={breadcrumb} />
        <div className="flex items-center gap-3">
          <Link to={ROUTES.MASTER_DATA.AGENCY()}>
            <Button variant="outline" className="w-full sm:w-auto">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Kembali
            </Button>
          </Link>
        </div>
      </div>

      <FormikProvider value={page.formik}>
        <div className="space-y-6">
          {/* Form Progress Indicator */}
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-2">
                  <div
                    className={`w-8 h-8 rounded-full flex items-center justify-center ${
                      page.formik.values.name ? 'bg-blue-100 text-blue-600' : 'bg-gray-100 text-gray-400'
                    }`}
                  >
                    <Building2 className="w-4 h-4" />
                  </div>
                  <span className="text-sm font-medium">Informasi Dasar</span>
                </div>
                <div className="flex-1 h-px bg-gray-200"></div>
                <div className="flex items-center gap-2">
                  <div
                    className={`w-8 h-8 rounded-full flex items-center justify-center ${
                      page.formik.values.lat && page.formik.values.lng
                        ? 'bg-blue-100 text-blue-600'
                        : 'bg-gray-100 text-gray-400'
                    }`}
                  >
                    <MapPin className="w-4 h-4" />
                  </div>
                  <span className="text-sm font-medium">Lokasi</span>
                </div>
                <div className="flex-1 h-px bg-gray-200"></div>
                <div className="flex items-center gap-2">
                  <div
                    className={`w-8 h-8 rounded-full flex items-center justify-center ${
                      page.formik.values.checked ? 'bg-green-100 text-green-600' : 'bg-gray-100 text-gray-400'
                    }`}
                  >
                    <CheckCircle className="w-4 h-4" />
                  </div>
                  <span className="text-sm font-medium">Konfirmasi</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Basic Information Section */}
          <Card>
            <CardHeader>
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                  <Building2 className="w-5 h-5 text-blue-600" />
                </div>
                <div>
                  <CardTitle className="text-lg">Informasi Dasar Instansi</CardTitle>
                  <p className="text-sm text-gray-600 mt-1">
                    Masukkan nama dan deskripsi instansi yang akan didaftarkan
                  </p>
                </div>
              </div>
            </CardHeader>
            <Separator />
            <CardContent className="pt-6">
              <div className="space-y-4">
                <InputText
                  required
                  id="name"
                  name="name"
                  label="Nama Instansi"
                  placeholder="Contoh: Dinas Perhubungan Kota Jakarta"
                />
                <InputTextArea
                  id="description"
                  name="description"
                  label="Deskripsi Instansi"
                  placeholder="Masukkan deskripsi singkat tentang instansi (opsional)"
                />
              </div>
            </CardContent>
          </Card>

          {/* Location Section */}
          <Card>
            <CardHeader>
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
                  <MapPin className="w-5 h-5 text-orange-600" />
                </div>
                <div>
                  <CardTitle className="text-lg">Lokasi Instansi</CardTitle>
                  <p className="text-sm text-gray-600 mt-1">
                    Tentukan lokasi geografis instansi untuk keperluan absensi
                  </p>
                </div>
              </div>
            </CardHeader>
            <Separator />
            <CardContent className="pt-6">
              <div className="space-y-4">
                {page.formik.values.lat && page.formik.values.lng && (
                  <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                    <div className="flex items-center gap-2 text-green-700">
                      <CheckCircle className="w-4 h-4" />
                      <span className="text-sm font-medium">Lokasi telah dipilih</span>
                    </div>
                    <div className="text-sm text-green-600 mt-1">
                      Koordinat: {page.formik.values.lat.toFixed(6)}, {page.formik.values.lng.toFixed(6)}
                    </div>
                  </div>
                )}
                <MapsSearch
                  value={{ lat: page.formik.values.lat, lng: page.formik.values.lng }}
                  onChange={(e) =>
                    page.formik.setValues({
                      ...page.formik.values,
                      lat: e.lat,
                      lng: e.lng,
                    })
                  }
                />
              </div>
            </CardContent>
          </Card>

          {/* Confirmation Section */}
          <Card>
            <CardHeader>
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                  <CheckCircle className="w-5 h-5 text-green-600" />
                </div>
                <div>
                  <CardTitle className="text-lg">Konfirmasi</CardTitle>
                  <p className="text-sm text-gray-600 mt-1">Pastikan semua data sudah benar sebelum menyimpan</p>
                </div>
              </div>
            </CardHeader>
            <Separator />
            <CardContent className="pt-6">
              <div className="space-y-6">
                {/* Form Validation Summary */}
                {!page.formik.isValid && page.formik.touched.name && (
                  <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                    <div className="flex items-center gap-2 text-red-700">
                      <AlertCircle className="w-4 h-4" />
                      <span className="text-sm font-medium">Mohon lengkapi data berikut:</span>
                    </div>
                    <ul className="text-sm text-red-600 mt-2 space-y-1">
                      {page.formik.errors.name && <li>• Nama instansi wajib diisi</li>}
                      {page.formik.errors.lat && <li>• Lokasi instansi wajib dipilih</li>}
                      {page.formik.errors.checked && <li>• Konfirmasi wajib dicentang</li>}
                    </ul>
                  </div>
                )}

                {/* Data Summary */}
                {page.formik.values.name && (
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h4 className="font-medium text-gray-900 mb-3">Ringkasan Data:</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Nama Instansi:</span>
                        <span className="font-medium">{page.formik.values.name}</span>
                      </div>
                      {page.formik.values.description && (
                        <div className="flex justify-between">
                          <span className="text-gray-600">Deskripsi:</span>
                          <span className="font-medium max-w-xs text-right">{page.formik.values.description}</span>
                        </div>
                      )}
                      {page.formik.values.lat && page.formik.values.lng && (
                        <div className="flex justify-between">
                          <span className="text-gray-600">Koordinat:</span>
                          <span className="font-medium">
                            {page.formik.values.lat.toFixed(4)}, {page.formik.values.lng.toFixed(4)}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {/* Confirmation Checkbox */}
                <div className="flex items-start gap-3 p-4 bg-blue-50 rounded-lg">
                  <Checkbox
                    checked={page.formik.values.checked}
                    onCheckedChange={() => page.formik.setFieldValue('checked', !page.formik.values.checked)}
                    id="confirmation"
                    className="mt-0.5"
                  />
                  <div className="flex-1">
                    <label htmlFor="confirmation" className="text-sm font-medium text-gray-900 cursor-pointer">
                      {isEdit ? 'Konfirmasi Perubahan Data' : 'Konfirmasi Penambahan Instansi'}
                    </label>
                    <p className="text-xs text-gray-600 mt-1">
                      {isEdit
                        ? 'Saya yakin untuk memperbarui data instansi dengan informasi yang telah dimasukkan'
                        : 'Saya yakin untuk menambahkan instansi baru dengan informasi yang telah dimasukkan'}
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
            <Separator />
            <CardFooter className="flex flex-col sm:flex-row gap-3 sm:justify-between">
              <Link to={ROUTES.MASTER_DATA.AGENCY()} className="w-full sm:w-auto">
                <Button variant="outline" className="w-full">
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Batal
                </Button>
              </Link>
              <Button
                onClick={() => page.formik.handleSubmit()}
                disabled={!page.formik.isValid || !page.formik.values.checked || page.formik.isSubmitting}
                loading={page.mutation.isPending}
                className="w-full sm:w-auto"
              >
                {isEdit ? (
                  <>
                    <Edit3 className="w-4 h-4 mr-2" />
                    Perbarui Instansi
                  </>
                ) : (
                  <>
                    <Save className="w-4 h-4 mr-2" />
                    Simpan Instansi
                  </>
                )}
              </Button>
            </CardFooter>
          </Card>
        </div>
      </FormikProvider>
    </PageContainer>
  );
}
