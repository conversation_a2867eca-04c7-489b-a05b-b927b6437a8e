import AppTable, { type ITableColumn } from '@/components/AppTable';
import PageContainer from '@/components/PageContainer.tsx';
import PageTitle from '@/components/page-title.tsx';
import { Button } from '@/components/ui/button.tsx';
import { ROUTES } from '@/routes/routes.ts';
import type { IResListAgency } from '@/types/response/IResListAgency';
import type { IBreadcrumbData } from '@/types/type/IBreadcrumbData.ts';
import { Link } from 'react-router-dom';
import { useAgencyPage } from './useAgencyPage';
import { MdEdit, MdInfo } from 'react-icons/md';

export default function AgencyPage() {
  const page = useAgencyPage();
  const breadcrumb: IBreadcrumbData[] = [
    {
      label: 'Master Data',
      path: ROUTES.MASTER_DATA.AGENCY(),
    },
    {
      label: 'Instansi',
      path: ROUTES.MASTER_DATA.AGENCY(),
    },
  ];

  const tableColumn: ITableColumn<IResListAgency>[] = [
    {
      headerTitle: 'Nama Instansi',
      component: (e) => (
        <div className="flex items-center gap-3">
          <div>
            <div className={'font-semibold'}>{e.name}</div>
            {e?.description && <p className={'text-gray-500'}>{e.description}</p>}
          </div>
        </div>
      ),
    },
    {
      headerTitle: '',
      component: (e) => (
        <div>
          <Link to={ROUTES.MASTER_DATA.EDIT_AGENCY(e?.id)}>
            <Button variant={'ghost'} size={'icon'}>
              <MdEdit />
            </Button>
          </Link>
          <Link to={ROUTES.DETAIL_AGENCY(e?.id)}>
            <Button variant={'ghost'} size={'icon'}>
              <MdInfo />
            </Button>
          </Link>
        </div>
      ),
    },
  ];
  return (
    <PageContainer>
      <div className={'flex justify-between'}>
        <PageTitle breadcrumb={breadcrumb} title={'Instansi'} />
        <Link to={ROUTES.MASTER_DATA.NEW_AGENCY()}>
          <Button>Tambah Instansi</Button>
        </Link>
      </div>
      <div>
        <AppTable loading={page.loadingList} data={page?.dataList || []} column={tableColumn} />
      </div>
    </PageContainer>
  );
}
