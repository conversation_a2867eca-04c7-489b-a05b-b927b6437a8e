import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';

export default function DetailWorkUnitSkeleton() {
  return (
    <div className="space-y-6">
      {/* Header Actions Skeleton */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div className="flex-1" />
        <div className="flex items-center gap-3">
          <Skeleton className="h-10 w-32" />
          <Skeleton className="h-10 w-24" />
        </div>
      </div>

      {/* Profile Header Skeleton */}
      <Card className="overflow-hidden">
        <div className="bg-gradient-to-r from-gray-200 to-gray-300 h-32 animate-pulse"></div>
        <CardContent className="relative pt-0 pb-6">
          <div className="flex flex-col sm:flex-row gap-6 -mt-16">
            <div className="relative">
              <Skeleton className="w-32 h-32 rounded-full border-4 border-white" />
            </div>
            
            <div className="flex-1 pt-4 sm:pt-8">
              <div className="flex flex-col pt-4 sm:flex-row sm:items-start sm:justify-between gap-4">
                <div className="flex-1">
                  <Skeleton className="h-8 w-64 mb-2" />
                  <div className="flex flex-wrap items-center gap-3 mb-3">
                    <Skeleton className="h-4 w-48" />
                  </div>
                  <Skeleton className="h-4 w-96" />
                </div>
                
                <div className="flex flex-col items-start sm:items-end gap-2">
                  <Skeleton className="h-6 w-24" />
                  <Skeleton className="h-4 w-32" />
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Information Cards Skeleton */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-40" />
          </CardHeader>
          <CardContent className="space-y-4">
            {[1, 2, 3].map((i) => (
              <div key={i}>
                <div className="flex items-center justify-between py-2">
                  <Skeleton className="h-4 w-32" />
                  <Skeleton className="h-4 w-40" />
                </div>
                {i < 3 && <div className="h-px bg-gray-200 my-2" />}
              </div>
            ))}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-32" />
          </CardHeader>
          <CardContent className="space-y-4">
            {[1, 2, 3].map((i) => (
              <div key={i}>
                <div className="flex items-center justify-between py-2">
                  <Skeleton className="h-4 w-28" />
                  <Skeleton className="h-6 w-20" />
                </div>
                {i < 3 && <div className="h-px bg-gray-200 my-2" />}
              </div>
            ))}
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions Skeleton */}
      <Card>
        <CardHeader>
          <Skeleton className="h-6 w-32" />
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            {[1, 2, 3, 4].map((i) => (
              <Skeleton key={i} className="h-20 w-full" />
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
