import { ENDPOINT } from '@/constants/endpoint';
import { useUi } from '@/hooks/useUi';
import ErrorService from '@/services/error.service';
import { HttpService } from '@/services/http.service';
import { useMutation, useQuery } from '@tanstack/react-query';
import { addWeeks, eachDayOfInterval, endOfWeek, format, startOfWeek, subWeeks } from 'date-fns';
import { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import type { BaseResponse } from '@/types/response/IResModel.ts';
import type { IResListShift } from '@/types/response/IResListShift.ts';
import type { IResListEmployee } from '@/types/response/IResListEmployee.ts';
import { WorkUnitRepository } from '@/repositories/work-unit-repositories.ts';
import type { IReqManageShift } from '@/types/request/IReqManageShift';

export function useSettingShiftTablePage() {
  const httpService = new HttpService();
  const errorService = new ErrorService();
  const workUnitRepository = new WorkUnitRepository();
  const [selectedDate, setSelectedDate] = useState<Date>(new Date());
  const [currentWeekStart, setCurrentWeekStart] = useState<Date>(startOfWeek(new Date(), { weekStartsOn: 1 }));

  const { toast } = useUi();
  const { id } = useParams();
  const daysOfWeek = eachDayOfInterval({
    start: currentWeekStart,
    end: endOfWeek(currentWeekStart, { weekStartsOn: 1 }),
  });

  const mutationManageShift = useMutation({
    mutationFn: (e: IReqManageShift[]) =>
      workUnitRepository.manageShift(id || '', e).then(() => {
        toast.success('Jadwal shift berhasil diubah');
      }),
  });

  const queryEmployee = useQuery({
    queryKey: ['employeeByWorkUnitId', id],
    enabled: !!id,
    queryFn: async () => {
      try {
        const res: BaseResponse<IResListEmployee[]> = await httpService.GET(
          ENDPOINT.EMPLOYEE_BY_WORK_UNIT_ID(id || ''),
        );
        return res.data.response_data || [];
      } catch (e) {
        errorService.fetchApiError(e);
        throw e;
      }
    },
  });

  const queryListSchedule = useQuery({
    queryKey: ['shift_schedule', id, currentWeekStart],
    enabled: !!id,
    queryFn: () =>
      workUnitRepository.listScheduleShift(id || '', {
        start: format(currentWeekStart, 'yyyy-MM-dd'),
        end: format(daysOfWeek[daysOfWeek.length - 1], 'yyyy-MM-dd'),
      }),
  });

  const queryShift = useQuery({
    queryKey: ['shiftByWorkUnitId', id],
    enabled: !!id,
    queryFn: async () => {
      try {
        const res: BaseResponse<IResListShift[]> = await httpService.GET(ENDPOINT.LIST_SHIFT_BY_WORK_UNIT_ID(id || ''));
        return res.data.response_data || [];
      } catch (e) {
        errorService.fetchApiError(e);
        throw e;
      }
    },
  });

  const goToPreviousWeek = () => {
    setCurrentWeekStart(subWeeks(currentWeekStart, 1));
  };

  const goToNextWeek = () => {
    setCurrentWeekStart(addWeeks(currentWeekStart, 1));
  };

  const formatDate = (date: Date) => {
    return format(date, 'd MMM');
  };

  const formatDay = (date: Date) => {
    return format(date, 'EEEE');
  };

  const dataEmployee = queryEmployee.data || [];
  const dataShift = queryShift.data || [];
  const dataScheduleShift = queryListSchedule.data || [];

  const [shiftSchedule, setShiftSchedule] = useState<Record<string, Record<string, string>>>({});

  // Convert API response to shiftSchedule format
  const convertApiDataToShiftSchedule = (scheduleData: any[]) => {
    const schedule: Record<string, Record<string, string>> = {};

    scheduleData.forEach((item) => {
      const accountId = item.account_id;
      const date = format(new Date(item.date), 'yyyy-MM-dd');
      const shiftId = item.shift_id;

      if (!schedule[accountId]) {
        schedule[accountId] = {};
      }

      schedule[accountId][date] = shiftId;
    });

    return schedule;
  };

  // Initialize shift schedule from API data when data is loaded (only once)
  const [isInitialized, setIsInitialized] = useState(false);

  useEffect(() => {
    if (dataScheduleShift.length > 0 && !isInitialized) {
      const convertedSchedule = convertApiDataToShiftSchedule(dataScheduleShift);
      setShiftSchedule(convertedSchedule);
      setIsInitialized(true);
    }
  }, [dataScheduleShift, isInitialized]);

  const generateFairShiftSchedule = () => {
    if (!dataEmployee.length || !dataShift.length) {
      return;
    }

    const newSchedule: Record<string, Record<string, string>> = {};
    const employeeShiftCounts: Record<string, Record<string, number>> = {};
    const employeeWorkDayCount: Record<string, number> = {};
    const employeeConsecutiveShifts: Record<string, string> = {};

    // Initialize data structures
    dataEmployee.forEach((employee: { account_id: string }) => {
      employeeShiftCounts[employee.account_id] = {};
      dataShift.forEach((shift: { id: { toString: () => string } }) => {
        employeeShiftCounts[employee.account_id][shift.id.toString()] = 0;
      });
      employeeShiftCounts[employee.account_id]['FREE'] = 0;
      employeeWorkDayCount[employee.account_id] = 0;
      newSchedule[employee.account_id] = {};
      employeeConsecutiveShifts[employee.account_id] = '';
    });

    // Constants for fair distribution
    const totalEmployees = dataEmployee.length;
    const totalShifts = dataShift.length;
    const totalDays = daysOfWeek.length;
    const workDaysPerEmployee = 5; // Each employee works exactly 5 days
    const freeDaysPerEmployee = 2; // Each employee gets exactly 2 days off

    // Calculate optimal distribution
    const totalWorkingSlots = totalEmployees * workDaysPerEmployee; // Total employee-days of work
    const workingSlotsPerDay = Math.floor(totalWorkingSlots / totalDays); // Average working employees per day
    const employeesPerShiftPerDay = Math.floor(workingSlotsPerDay / totalShifts); // Base employees per shift per day
    const minEmployeesPerShift = Math.max(1, employeesPerShiftPerDay); // Minimum 1 employee per shift
    const maxEmployeesPerShift = employeesPerShiftPerDay + 2; // Allow some flexibility

    console.log('Distribution targets:', {
      totalEmployees,
      totalShifts,
      totalDays,
      workDaysPerEmployee,
      totalWorkingSlots,
      workingSlotsPerDay,
      employeesPerShiftPerDay,
      minEmployeesPerShift,
      maxEmployeesPerShift,
    });

    // Step 1: Pre-assign days off to ensure each employee gets exactly 2 days off
    const employeeDaysOff: Record<string, number[]> = {};

    dataEmployee.forEach((employee: { account_id: string }, employeeIndex: number) => {
      const employeeId = employee.account_id;
      employeeDaysOff[employeeId] = [];

      // Distribute days off fairly across the week
      // Use different patterns to ensure variety
      const patterns = [
        [5, 6], // Weekend
        [0, 6], // Monday + Sunday
        [1, 5], // Tuesday + Saturday
        [2, 6], // Wednesday + Sunday
        [3, 5], // Thursday + Saturday
        [4, 6], // Friday + Sunday
        [0, 3], // Monday + Thursday
        [1, 4], // Tuesday + Friday
      ];

      const patternIndex = employeeIndex % patterns.length;
      employeeDaysOff[employeeId] = patterns[patternIndex];

      // Initialize work day count
      employeeWorkDayCount[employeeId] = 0;
    });

    // Step 2: Process each day to ensure balanced shift distribution
    daysOfWeek.forEach((day, dayIndex) => {
      const dayKey = format(day, 'yyyy-MM-dd');

      // Track daily shift assignments
      const dailyShiftCounts: Record<string, number> = {};
      const dailyShiftAssignments: Record<string, string[]> = {};

      dataShift.forEach((shift: { id: { toString: () => string } }) => {
        const shiftId = shift.id.toString();
        dailyShiftCounts[shiftId] = 0;
        dailyShiftAssignments[shiftId] = [];
      });

      // Get employees available for work today (not on their days off)
      const availableEmployees = dataEmployee
        .filter((employee: { account_id: string }) => {
          const employeeId = employee.account_id;
          return !employeeDaysOff[employeeId].includes(dayIndex);
        })
        .map((emp: { account_id: string }) => emp.account_id);

      // Assign days off first
      dataEmployee.forEach((employee: { account_id: string }) => {
        const employeeId = employee.account_id;
        if (employeeDaysOff[employeeId].includes(dayIndex)) {
          newSchedule[employeeId][dayKey] = 'FREE';
          employeeShiftCounts[employeeId]['FREE']++;
        }
      });

      console.log(`Day ${dayIndex} (${dayKey}): ${availableEmployees.length} employees available for work`);

      // Step 3: Distribute available employees across shifts using round-robin approach
      const shiftIds = dataShift.map((shift: { id: { toString: () => string } }) => shift.id.toString());

      // First pass: Ensure minimum coverage (at least 1 employee per shift)
      shiftIds.forEach((shiftId) => {
        if (availableEmployees.length > 0 && dailyShiftCounts[shiftId] < minEmployeesPerShift) {
          // Find best employee for this shift
          let bestEmployeeIndex = 0;
          let bestScore = Infinity;

          availableEmployees.forEach((employeeId, index) => {
            let score = 0;

            // Priority 1: Employees with fewer total assignments to this shift type
            score += employeeShiftCounts[employeeId][shiftId] * 100;

            // Priority 2: Avoid consecutive same shifts
            if (employeeConsecutiveShifts[employeeId] === shiftId) {
              score += 1000;
            }

            // Priority 3: Balance overall shift distribution for employee
            const totalShiftsForEmployee = Object.values(employeeShiftCounts[employeeId])
              .filter((_, index) => index < shiftIds.length) // Exclude FREE days
              .reduce((sum, count) => sum + count, 0);

            if (totalShiftsForEmployee > 0) {
              const shiftRatio = employeeShiftCounts[employeeId][shiftId] / totalShiftsForEmployee;
              score += shiftRatio * 50;
            }

            // Priority 4: Employees who need more work days
            const currentWorkDays = employeeWorkDayCount[employeeId];
            if (currentWorkDays < workDaysPerEmployee) {
              score -= (workDaysPerEmployee - currentWorkDays) * 10;
            }

            if (score < bestScore) {
              bestScore = score;
              bestEmployeeIndex = index;
            }
          });

          const selectedEmployee = availableEmployees[bestEmployeeIndex];
          newSchedule[selectedEmployee][dayKey] = shiftId;
          employeeShiftCounts[selectedEmployee][shiftId]++;
          employeeWorkDayCount[selectedEmployee]++;
          employeeConsecutiveShifts[selectedEmployee] = shiftId;
          dailyShiftCounts[shiftId]++;
          dailyShiftAssignments[shiftId].push(selectedEmployee);

          // Remove employee from available list
          availableEmployees.splice(bestEmployeeIndex, 1);
        }
      });

      // Second pass: Distribute remaining employees using round-robin to balance shifts
      let shiftIndex = 0;
      while (availableEmployees.length > 0) {
        const currentShiftId = shiftIds[shiftIndex % shiftIds.length];

        // Check if this shift can accept more employees
        if (dailyShiftCounts[currentShiftId] < maxEmployeesPerShift) {
          // Find best employee for this shift
          let bestEmployeeIndex = 0;
          let bestScore = Infinity;

          availableEmployees.forEach((employeeId, index) => {
            let score = 0;

            // Priority 1: Employees with fewer assignments to this shift type
            score += employeeShiftCounts[employeeId][currentShiftId] * 100;

            // Priority 2: Avoid consecutive same shifts
            if (employeeConsecutiveShifts[employeeId] === currentShiftId) {
              score += 500;
            }

            // Priority 3: Employees who need more work days
            const currentWorkDays = employeeWorkDayCount[employeeId];
            if (currentWorkDays < workDaysPerEmployee) {
              score -= (workDaysPerEmployee - currentWorkDays) * 20;
            }

            if (score < bestScore) {
              bestScore = score;
              bestEmployeeIndex = index;
            }
          });

          const selectedEmployee = availableEmployees[bestEmployeeIndex];
          newSchedule[selectedEmployee][dayKey] = currentShiftId;
          employeeShiftCounts[selectedEmployee][currentShiftId]++;
          employeeWorkDayCount[selectedEmployee]++;
          employeeConsecutiveShifts[selectedEmployee] = currentShiftId;
          dailyShiftCounts[currentShiftId]++;
          dailyShiftAssignments[currentShiftId].push(selectedEmployee);

          // Remove employee from available list
          availableEmployees.splice(bestEmployeeIndex, 1);
        }

        shiftIndex++;

        // Safety check: if we've cycled through all shifts and no assignments were made
        if (shiftIndex > shiftIds.length * 2 && availableEmployees.length > 0) {
          // Force assign remaining employees to least populated shifts
          const leastPopulatedShift = shiftIds.reduce((min, shiftId) =>
            dailyShiftCounts[shiftId] < dailyShiftCounts[min] ? shiftId : min,
          );

          const employeeId = availableEmployees[0];
          newSchedule[employeeId][dayKey] = leastPopulatedShift;
          employeeShiftCounts[employeeId][leastPopulatedShift]++;
          employeeWorkDayCount[employeeId]++;
          employeeConsecutiveShifts[employeeId] = leastPopulatedShift;
          dailyShiftCounts[leastPopulatedShift]++;

          availableEmployees.splice(0, 1);
        }
      }

      // Validation: Check daily distribution
      const totalAssigned = Object.values(dailyShiftCounts).reduce((sum, count) => sum + count, 0);
      const expectedWorking =
        dataEmployee.length -
        dataEmployee.filter((emp: { account_id: string }) => employeeDaysOff[emp.account_id].includes(dayIndex)).length;

      console.log(`Day ${dayIndex} (${dayKey}) distribution:`, {
        shifts: dailyShiftCounts,
        totalAssigned,
        expectedWorking,
        balanced: totalAssigned === expectedWorking,
      });
    });

    // Final validation and summary
    console.log('\n=== FINAL SCHEDULE VALIDATION ===');

    // Validate each employee has exactly 5 work days
    let validationPassed = true;
    const employeeValidation: Record<string, { workDays: number; freeDays: number; shifts: Record<string, number> }> =
      {};

    dataEmployee.forEach((employee: { account_id: string }) => {
      const employeeId = employee.account_id;
      let workDays = 0;
      let freeDays = 0;
      const shiftCounts: Record<string, number> = {};

      // Initialize shift counts
      dataShift.forEach((shift: { id: { toString: () => string } }) => {
        shiftCounts[shift.id.toString()] = 0;
      });
      shiftCounts['FREE'] = 0;

      // Count actual assignments
      daysOfWeek.forEach((day) => {
        const dayKey = format(day, 'yyyy-MM-dd');
        const assignment = newSchedule[employeeId][dayKey];

        if (assignment === 'FREE') {
          freeDays++;
          shiftCounts['FREE']++;
        } else {
          workDays++;
          shiftCounts[assignment]++;
        }
      });

      employeeValidation[employeeId] = { workDays, freeDays, shifts: shiftCounts };

      if (workDays !== workDaysPerEmployee || freeDays !== freeDaysPerEmployee) {
        console.error(
          `❌ Employee ${employeeId}: ${workDays} work days, ${freeDays} free days (expected: ${workDaysPerEmployee} work, ${freeDaysPerEmployee} free)`,
        );
        validationPassed = false;
      }
    });

    // Validate daily shift coverage
    const dailyValidation: Record<string, { total: number; shifts: Record<string, number> }> = {};

    daysOfWeek.forEach((day, dayIndex) => {
      const dayKey = format(day, 'yyyy-MM-dd');
      const shiftCounts: Record<string, number> = {};
      let totalWorking = 0;

      // Initialize shift counts
      dataShift.forEach((shift: { id: { toString: () => string } }) => {
        shiftCounts[shift.id.toString()] = 0;
      });

      // Count assignments for this day
      dataEmployee.forEach((employee: { account_id: string }) => {
        const assignment = newSchedule[employee.account_id][dayKey];
        if (assignment !== 'FREE') {
          shiftCounts[assignment]++;
          totalWorking++;
        }
      });

      dailyValidation[dayKey] = { total: totalWorking, shifts: shiftCounts };

      // Check if any shift has 0 employees
      Object.entries(shiftCounts).forEach(([shiftId, count]) => {
        if (count === 0) {
          console.error(`❌ Day ${dayIndex} (${dayKey}): Shift ${shiftId} has 0 employees`);
          validationPassed = false;
        }
      });
    });

    // Print summary
    console.log('\n=== SCHEDULE SUMMARY ===');
    console.log('Employee validation:', employeeValidation);
    console.log('Daily validation:', dailyValidation);
    console.log('Overall validation passed:', validationPassed);

    if (!validationPassed) {
      console.error('❌ Schedule validation failed! Please check the algorithm.');
    } else {
      console.log('✅ Schedule validation passed! All requirements met.');
    }

    // Update local state first
    setShiftSchedule(newSchedule);
    console.log('Final schedule:', newSchedule);

    // Prepare batch data for API submission
    const batchShiftData: IReqManageShift[] = [];

    // Iterate through all generated shift assignments
    Object.keys(newSchedule).forEach((employeeId) => {
      Object.keys(newSchedule[employeeId]).forEach((dateKey) => {
        const shiftId = newSchedule[employeeId][dateKey];

        // Only send non-FREE shifts to API
        if (shiftId && shiftId !== 'FREE') {
          const shiftData: IReqManageShift = {
            account_id: employeeId,
            date: dateKey,
            shift_id: shiftId,
            is_holiday: false,
          };
          batchShiftData.push(shiftData);
        }
      });
    });

    if (batchShiftData.length > 0) {
      console.log('Sending batch shift data to API:', batchShiftData);
      mutationManageShift.mutate(batchShiftData);
    }

    toast.success('Jadwal shift berhasil di generate secara adil');
  };

  const getEmployeeShift = (employeeId: string, day: Date) => {
    const dayKey = format(day, 'yyyy-MM-dd');

    // First check current state (user modifications take priority)
    const currentShift = shiftSchedule[employeeId]?.[dayKey];
    if (currentShift !== undefined) {
      return currentShift;
    }

    const apiShift = dataScheduleShift.find(
      (item) => item.account_id === employeeId && format(new Date(item.date), 'yyyy-MM-dd') === dayKey,
    );

    if (apiShift) {
      return apiShift.shift_id;
    }

    return 'FREE';
  };

  // Get shift name by shift ID
  const getShiftName = (shiftId: string) => {
    if (!shiftId || shiftId === 'FREE') return 'Libur';

    // First check from API data
    const apiShift = dataScheduleShift.find((item) => item.shift_id === shiftId);
    if (apiShift) {
      return apiShift.shift_name;
    }

    // Fallback to local shift data
    const shift = dataShift.find((s) => s.id === shiftId);
    return shift?.name || 'Unknown Shift';
  };

  // Get employee name by account ID
  const getEmployeeName = (accountId: string) => {
    // First check from API data
    const apiEmployee = dataScheduleShift.find((item) => item.account_id === accountId);
    if (apiEmployee) {
      return apiEmployee.account_name;
    }

    // Fallback to local employee data
    const employee = dataEmployee.find((emp) => emp.account_id === accountId);
    return employee?.name || 'Unknown Employee';
  };

  const updateEmployeeShift = (employeeId: string, day: Date, shiftId: string) => {
    const dayKey = format(day, 'yyyy-MM-dd');

    setShiftSchedule((prevSchedule) => {
      const newSchedule = { ...prevSchedule };

      if (!newSchedule[employeeId]) {
        newSchedule[employeeId] = {};
      }

      newSchedule[employeeId][dayKey] = shiftId;

      return newSchedule;
    });
  };

  const getEmployeeWorkDays = (employeeId: string) => {
    let workDays = 0;
    let freeDays = 0;

    daysOfWeek.forEach((day) => {
      const shift = getEmployeeShift(employeeId, day);
      if (shift && shift !== 'FREE') {
        workDays++;
      } else if (shift === 'FREE') {
        freeDays++;
      }
    });

    return { workDays, freeDays };
  };

  const getShiftCountsForDay = (day: Date) => {
    const counts: Record<string, number> = { FREE: 0 };

    dataShift.forEach((shift: { id: { toString: () => string } }) => {
      counts[shift.id.toString()] = 0;
    });

    dataEmployee.forEach((employee: { account_id: string }) => {
      const shift = getEmployeeShift(employee.account_id, day);
      if (shift) {
        counts[shift] = (counts[shift] || 0) + 1;
      }
    });

    return counts;
  };

  function onChangeValueShift(value: string, employee: IResListEmployee, dayKey: string) {
    const newSchedule = { ...shiftSchedule };
    if (!newSchedule[employee.account_id]) {
      newSchedule[employee.account_id] = {};
    }
    newSchedule[employee.account_id][dayKey] = value;
    if (value !== 'FREE') {
      const data: IReqManageShift = {
        account_id: employee.account_id,
        date: dayKey,
        shift_id: value,
        is_holiday: false,
      };
      mutationManageShift.mutate([data]);
    }
    console.log(newSchedule);
    setShiftSchedule(newSchedule);
  }

  return {
    dataEmployee,
    dataShift,
    dataScheduleShift,
    selectedDate,
    setSelectedDate,
    currentWeekStart,
    setCurrentWeekStart,
    daysOfWeek,
    goToPreviousWeek,
    goToNextWeek,
    formatDate,
    formatDay,
    generateFairShiftSchedule,
    getEmployeeShift,
    getShiftName,
    getEmployeeName,
    updateEmployeeShift,
    shiftSchedule,
    setShiftSchedule,
    getEmployeeWorkDays,
    getShiftCountsForDay,
    queryEmployee,
    queryListSchedule,
    convertApiDataToShiftSchedule,
    onChangeValueShift,
    isInitialized,
  };
}
