import { ENDPOINT } from '@/constants/endpoint';
import { useUi } from '@/hooks/useUi';
import ErrorService from '@/services/error.service';
import { HttpService } from '@/services/http.service';
import { useQuery } from '@tanstack/react-query';
import { addDays, addWeeks, eachDayOfInterval, endOfWeek, format, startOfWeek, subWeeks } from 'date-fns';
import { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import type { BaseResponse } from '@/types/response/IResModel.ts';
import type { IResListShift } from '@/types/response/IResListShift.ts';
import type { IResListEmployee } from '@/types/response/IResListEmployee.ts';
import { WorkUnitRepository } from '@/repositories/work-unit-repositories.ts';

export function useSettingShiftTablePage() {
  const httpService = new HttpService();
  const errorService = new ErrorService();
  const workUnitRepository = new WorkUnitRepository();
  const [selectedDate, setSelectedDate] = useState<Date>(new Date());
  const [currentWeekStart, setCurrentWeekStart] = useState<Date>(startOfWeek(new Date(), { weekStartsOn: 1 }));

  const { toast } = useUi();
  const { id } = useParams();
  const daysOfWeek = eachDayOfInterval({
    start: currentWeekStart,
    end: endOfWeek(currentWeekStart, { weekStartsOn: 1 }),
  });

  const queryEmployee = useQuery({
    queryKey: ['employeeByWorkUnitId', id],
    enabled: !!id,
    queryFn: async () => {
      try {
        const res: BaseResponse<IResListEmployee[]> = await httpService.GET(
          ENDPOINT.EMPLOYEE_BY_WORK_UNIT_ID(id || ''),
        );
        return res.data.response_data || [];
      } catch (e) {
        errorService.fetchApiError(e);
        throw e;
      }
    },
  });

  const queryListSchedule = useQuery({
    queryKey: ['shift_schedule', id, currentWeekStart],
    enabled: !!id,
    queryFn: () =>
      workUnitRepository.listScheduleShift(id || '', {
        start: format(currentWeekStart, 'yyyy-MM-dd'),
        end: format(daysOfWeek[daysOfWeek.length - 1], 'yyyy-MM-dd'),
      }),
  });

  const queryShift = useQuery({
    queryKey: ['shiftByWorkUnitId', id],
    enabled: !!id,
    queryFn: async () => {
      try {
        const res: BaseResponse<IResListShift[]> = await httpService.GET(ENDPOINT.LIST_SHIFT_BY_WORK_UNIT_ID(id || ''));
        return res.data.response_data || [];
      } catch (e) {
        errorService.fetchApiError(e);
        throw e;
      }
    },
  });

  const goToPreviousWeek = () => {
    setCurrentWeekStart(subWeeks(currentWeekStart, 1));
  };

  const goToNextWeek = () => {
    setCurrentWeekStart(addWeeks(currentWeekStart, 1));
  };

  const formatDate = (date: Date) => {
    return format(date, 'd MMM');
  };

  const formatDay = (date: Date) => {
    return format(date, 'EEEE');
  };

  const dataEmployee = queryEmployee.data || [];
  const dataShift = queryShift.data || [];

  const [shiftSchedule, setShiftSchedule] = useState<Record<string, Record<string, string>>>({});

  const generateFairShiftSchedule = () => {
    if (!dataEmployee.length || !dataShift.length) {
      return;
    }

    const newSchedule: Record<string, Record<string, string>> = {};

    const employeeShiftCounts: Record<string, Record<string, number>> = {};
    dataEmployee.forEach((employee: { account_id: string }) => {
      employeeShiftCounts[employee.account_id] = {};
      dataShift.forEach((shift: { id: { toString: () => string } }) => {
        employeeShiftCounts[employee.account_id][shift.id.toString()] = 0;
      });
      employeeShiftCounts[employee.account_id]['FREE'] = 0;

      newSchedule[employee.account_id] = {};
    });

    dataEmployee.forEach((employee: { account_id: string }, employeeIndex: number) => {
      const daysOff: any[] = [];

      const firstDayOff = employeeIndex % 5;
      daysOff.push(firstDayOff);

      const secondDayOff = employeeIndex % 2 === 0 ? 5 : 6;
      daysOff.push(secondDayOff);

      daysOfWeek.forEach((day, dayIndex) => {
        const dayKey = format(day, 'yyyy-MM-dd');

        if (daysOff.includes(dayIndex)) {
          newSchedule[employee.account_id][dayKey] = 'FREE';
          employeeShiftCounts[employee.account_id]['FREE']++;
        } else {
          let fairestShift = '';

          let minShiftCount = Infinity;

          const shiftsWithRandomOrder = [...dataShift]
            .sort(() => Math.random() - 0.5)
            .map((shift) => shift.id.toString());

          shiftsWithRandomOrder.forEach((shiftId) => {
            if (employeeShiftCounts[employee.account_id][shiftId] < minShiftCount) {
              minShiftCount = employeeShiftCounts[employee.account_id][shiftId];
              fairestShift = shiftId;
            }
          });

          const yesterdayKey = format(addDays(day, -1), 'yyyy-MM-dd');
          if (newSchedule[employee.account_id][yesterdayKey] === fairestShift) {
            fairestShift = shiftsWithRandomOrder.find((shiftId) => shiftId !== fairestShift) || fairestShift;
          }

          newSchedule[employee.account_id][dayKey] = fairestShift;
          employeeShiftCounts[employee.account_id][fairestShift]++;
        }
      });
    });

    daysOfWeek.forEach((day, dayIndex) => {
      const dayKey = format(day, 'yyyy-MM-dd');

      if (dayIndex >= 5) {
        const workingEmployees = dataEmployee.filter(
          (employee: { account_id: string }) => newSchedule[employee.account_id][dayKey] !== 'FREE',
        );

        const minWorkingEmployees = Math.ceil(dataEmployee.length * 0.3);

        if (workingEmployees.length < minWorkingEmployees) {
          const employeesToWork = dataEmployee
            .filter((employee: { account_id: string }) => newSchedule[employee.account_id][dayKey] === 'FREE')
            .slice(0, minWorkingEmployees - workingEmployees.length);

          employeesToWork.forEach((employee: { account_id: string }) => {
            newSchedule[employee.account_id][dayKey] =
              dataShift[Math.floor(Math.random() * dataShift.length)].id.toString();

            const weekdayKeys = daysOfWeek.filter((_d, i) => i < 5).map((d) => format(d, 'yyyy-MM-dd'));

            for (const wdKey of weekdayKeys) {
              if (newSchedule[employee.account_id][wdKey] !== 'FREE') {
                newSchedule[employee.account_id][wdKey] = 'FREE';
                break;
              }
            }
          });
        }
      }
    });

    setShiftSchedule(newSchedule);
    console.log(newSchedule);
    toast.success('Jadwal shift berhasil di generate secara adil');
  };

  const getEmployeeShift = (employeeId: string, day: Date) => {
    const dayKey = format(day, 'yyyy-MM-dd');
    // Jika jadwal sudah di-generate, gunakan nilai yang ada
    // Jika belum, kembalikan string kosong (tidak ada shift)
    return shiftSchedule[employeeId]?.[dayKey] || '';
  };

  const getEmployeeWorkDays = (employeeId: string) => {
    let workDays = 0;
    let freeDays = 0;

    daysOfWeek.forEach((day) => {
      const shift = getEmployeeShift(employeeId, day);
      if (shift && shift !== 'FREE') {
        workDays++;
      } else if (shift === 'FREE') {
        freeDays++;
      }
    });

    return { workDays, freeDays };
  };

  const getShiftCountsForDay = (day: Date) => {
    const counts: Record<string, number> = { FREE: 0 };

    dataShift.forEach((shift: { id: { toString: () => string } }) => {
      counts[shift.id.toString()] = 0;
    });

    dataEmployee.forEach((employee: { account_id: string }) => {
      const shift = getEmployeeShift(employee.account_id, day);
      if (shift) {
        counts[shift] = (counts[shift] || 0) + 1;
      }
    });

    return counts;
  };

  useEffect(() => {
    console.log(queryListSchedule.data);
  }, [queryListSchedule.data]);

  return {
    dataEmployee,
    dataShift,
    selectedDate,
    setSelectedDate,
    currentWeekStart,
    setCurrentWeekStart,
    daysOfWeek,
    goToPreviousWeek,
    goToNextWeek,
    formatDate,
    formatDay,
    generateFairShiftSchedule,
    getEmployeeShift,
    shiftSchedule,
    setShiftSchedule,
    getEmployeeWorkDays,
    getShiftCountsForDay,
    queryEmployee,
    queryListSchedule,
  };
}
