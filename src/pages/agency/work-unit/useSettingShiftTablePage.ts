import { ENDPOINT } from '@/constants/endpoint';
import { useUi } from '@/hooks/useUi';
import ErrorService from '@/services/error.service';
import { HttpService } from '@/services/http.service';
import { useMutation, useQuery } from '@tanstack/react-query';
import { addDays, addWeeks, eachDayOfInterval, endOfWeek, format, startOfWeek, subWeeks } from 'date-fns';
import { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import type { BaseResponse } from '@/types/response/IResModel.ts';
import type { IResListShift } from '@/types/response/IResListShift.ts';
import type { IResListEmployee } from '@/types/response/IResListEmployee.ts';
import { WorkUnitRepository } from '@/repositories/work-unit-repositories.ts';
import type { IReqManageShift } from '@/types/request/IReqManageShift';

export function useSettingShiftTablePage() {
  const httpService = new HttpService();
  const errorService = new ErrorService();
  const workUnitRepository = new WorkUnitRepository();
  const [selectedDate, setSelectedDate] = useState<Date>(new Date());
  const [currentWeekStart, setCurrentWeekStart] = useState<Date>(startOfWeek(new Date(), { weekStartsOn: 1 }));

  const { toast } = useUi();
  const { id } = useParams();
  const daysOfWeek = eachDayOfInterval({
    start: currentWeekStart,
    end: endOfWeek(currentWeekStart, { weekStartsOn: 1 }),
  });

  const mutationManageShift = useMutation({
    mutationFn: (e: IReqManageShift[]) =>
      workUnitRepository.manageShift(id || '', e).then(() => {
        toast.success('Jadwal shift berhasil diubah');
      }),
  });

  const queryEmployee = useQuery({
    queryKey: ['employeeByWorkUnitId', id],
    enabled: !!id,
    queryFn: async () => {
      try {
        const res: BaseResponse<IResListEmployee[]> = await httpService.GET(
          ENDPOINT.EMPLOYEE_BY_WORK_UNIT_ID(id || ''),
        );
        return res.data.response_data || [];
      } catch (e) {
        errorService.fetchApiError(e);
        throw e;
      }
    },
  });

  const queryListSchedule = useQuery({
    queryKey: ['shift_schedule', id, currentWeekStart],
    enabled: !!id,
    queryFn: () =>
      workUnitRepository.listScheduleShift(id || '', {
        start: format(currentWeekStart, 'yyyy-MM-dd'),
        end: format(daysOfWeek[daysOfWeek.length - 1], 'yyyy-MM-dd'),
      }),
  });

  const queryShift = useQuery({
    queryKey: ['shiftByWorkUnitId', id],
    enabled: !!id,
    queryFn: async () => {
      try {
        const res: BaseResponse<IResListShift[]> = await httpService.GET(ENDPOINT.LIST_SHIFT_BY_WORK_UNIT_ID(id || ''));
        return res.data.response_data || [];
      } catch (e) {
        errorService.fetchApiError(e);
        throw e;
      }
    },
  });

  const goToPreviousWeek = () => {
    setCurrentWeekStart(subWeeks(currentWeekStart, 1));
  };

  const goToNextWeek = () => {
    setCurrentWeekStart(addWeeks(currentWeekStart, 1));
  };

  const formatDate = (date: Date) => {
    return format(date, 'd MMM');
  };

  const formatDay = (date: Date) => {
    return format(date, 'EEEE');
  };

  const dataEmployee = queryEmployee.data || [];
  const dataShift = queryShift.data || [];
  const dataScheduleShift = queryListSchedule.data || [];

  const [shiftSchedule, setShiftSchedule] = useState<Record<string, Record<string, string>>>({});

  // Convert API response to shiftSchedule format
  const convertApiDataToShiftSchedule = (scheduleData: any[]) => {
    const schedule: Record<string, Record<string, string>> = {};

    scheduleData.forEach((item) => {
      const accountId = item.account_id;
      const date = format(new Date(item.date), 'yyyy-MM-dd');
      const shiftId = item.shift_id;

      if (!schedule[accountId]) {
        schedule[accountId] = {};
      }

      schedule[accountId][date] = shiftId;
    });

    return schedule;
  };

  // Initialize shift schedule from API data when data is loaded
  useEffect(() => {
    if (dataScheduleShift.length > 0) {
      const convertedSchedule = convertApiDataToShiftSchedule(dataScheduleShift);
      setShiftSchedule(convertedSchedule);
    }
  }, [dataScheduleShift]);

  const generateFairShiftSchedule = () => {
    if (!dataEmployee.length || !dataShift.length) {
      return;
    }

    const newSchedule: Record<string, Record<string, string>> = {};

    const employeeShiftCounts: Record<string, Record<string, number>> = {};
    dataEmployee.forEach((employee: { account_id: string }) => {
      employeeShiftCounts[employee.account_id] = {};
      dataShift.forEach((shift: { id: { toString: () => string } }) => {
        employeeShiftCounts[employee.account_id][shift.id.toString()] = 0;
      });
      employeeShiftCounts[employee.account_id]['FREE'] = 0;

      newSchedule[employee.account_id] = {};
    });

    dataEmployee.forEach((employee: { account_id: string }, employeeIndex: number) => {
      const daysOff: any[] = [];

      const firstDayOff = employeeIndex % 5;
      daysOff.push(firstDayOff);

      const secondDayOff = employeeIndex % 2 === 0 ? 5 : 6;
      daysOff.push(secondDayOff);

      daysOfWeek.forEach((day, dayIndex) => {
        const dayKey = format(day, 'yyyy-MM-dd');

        if (daysOff.includes(dayIndex)) {
          newSchedule[employee.account_id][dayKey] = 'FREE';
          employeeShiftCounts[employee.account_id]['FREE']++;
        } else {
          let fairestShift = '';

          let minShiftCount = Infinity;

          const shiftsWithRandomOrder = [...dataShift]
            .sort(() => Math.random() - 0.5)
            .map((shift) => shift.id.toString());

          shiftsWithRandomOrder.forEach((shiftId) => {
            if (employeeShiftCounts[employee.account_id][shiftId] < minShiftCount) {
              minShiftCount = employeeShiftCounts[employee.account_id][shiftId];
              fairestShift = shiftId;
            }
          });

          const yesterdayKey = format(addDays(day, -1), 'yyyy-MM-dd');
          if (newSchedule[employee.account_id][yesterdayKey] === fairestShift) {
            fairestShift = shiftsWithRandomOrder.find((shiftId) => shiftId !== fairestShift) || fairestShift;
          }

          newSchedule[employee.account_id][dayKey] = fairestShift;
          employeeShiftCounts[employee.account_id][fairestShift]++;
        }
      });
    });

    daysOfWeek.forEach((day, dayIndex) => {
      const dayKey = format(day, 'yyyy-MM-dd');

      if (dayIndex >= 5) {
        const workingEmployees = dataEmployee.filter(
          (employee: { account_id: string }) => newSchedule[employee.account_id][dayKey] !== 'FREE',
        );

        const minWorkingEmployees = Math.ceil(dataEmployee.length * 0.3);

        if (workingEmployees.length < minWorkingEmployees) {
          const employeesToWork = dataEmployee
            .filter((employee: { account_id: string }) => newSchedule[employee.account_id][dayKey] === 'FREE')
            .slice(0, minWorkingEmployees - workingEmployees.length);

          employeesToWork.forEach((employee: { account_id: string }) => {
            newSchedule[employee.account_id][dayKey] =
              dataShift[Math.floor(Math.random() * dataShift.length)].id.toString();

            const weekdayKeys = daysOfWeek.filter((_d, i) => i < 5).map((d) => format(d, 'yyyy-MM-dd'));

            for (const wdKey of weekdayKeys) {
              if (newSchedule[employee.account_id][wdKey] !== 'FREE') {
                newSchedule[employee.account_id][wdKey] = 'FREE';
                break;
              }
            }
          });
        }
      }
    });

    setShiftSchedule(newSchedule);
    console.log(newSchedule);
    toast.success('Jadwal shift berhasil di generate secara adil');
  };

  const getEmployeeShift = (employeeId: string, day: Date) => {
    const dayKey = format(day, 'yyyy-MM-dd');

    // First check if there's data from API (existing schedule)
    const apiShift = dataScheduleShift.find(
      (item) => item.account_id === employeeId && format(new Date(item.date), 'yyyy-MM-dd') === dayKey,
    );

    if (apiShift) {
      return apiShift.shift_id;
    }

    // If no API data, check generated schedule
    return shiftSchedule[employeeId]?.[dayKey] || '';
  };

  // Get shift name by shift ID
  const getShiftName = (shiftId: string) => {
    if (!shiftId || shiftId === 'FREE') return 'Libur';

    // First check from API data
    const apiShift = dataScheduleShift.find((item) => item.shift_id === shiftId);
    if (apiShift) {
      return apiShift.shift_name;
    }

    // Fallback to local shift data
    const shift = dataShift.find((s) => s.id === shiftId);
    return shift?.name || 'Unknown Shift';
  };

  // Get employee name by account ID
  const getEmployeeName = (accountId: string) => {
    // First check from API data
    const apiEmployee = dataScheduleShift.find((item) => item.account_id === accountId);
    if (apiEmployee) {
      return apiEmployee.account_name;
    }

    // Fallback to local employee data
    const employee = dataEmployee.find((emp) => emp.account_id === accountId);
    return employee?.name || 'Unknown Employee';
  };

  const getEmployeeWorkDays = (employeeId: string) => {
    let workDays = 0;
    let freeDays = 0;

    daysOfWeek.forEach((day) => {
      const shift = getEmployeeShift(employeeId, day);
      if (shift && shift !== 'FREE') {
        workDays++;
      } else if (shift === 'FREE') {
        freeDays++;
      }
    });

    return { workDays, freeDays };
  };

  const getShiftCountsForDay = (day: Date) => {
    const counts: Record<string, number> = { FREE: 0 };

    dataShift.forEach((shift: { id: { toString: () => string } }) => {
      counts[shift.id.toString()] = 0;
    });

    dataEmployee.forEach((employee: { account_id: string }) => {
      const shift = getEmployeeShift(employee.account_id, day);
      if (shift) {
        counts[shift] = (counts[shift] || 0) + 1;
      }
    });

    return counts;
  };

  function onChangeValueShift(value: string, employee: IResListEmployee, dayKey: string) {
    const newSchedule = { ...shiftSchedule };
    if (!newSchedule[employee.account_id]) {
      newSchedule[employee.account_id] = {};
    }
    newSchedule[employee.account_id][dayKey] = value;
    if (value !== 'FREE') {
      const data: IReqManageShift = {
        account_id: employee.account_id,
        date: dayKey,
        shift_id: value,
        is_holiday: false,
      };
      console.log(data);
    }
    setShiftSchedule(newSchedule);
  }

  return {
    dataEmployee,
    dataShift,
    dataScheduleShift,
    selectedDate,
    setSelectedDate,
    currentWeekStart,
    setCurrentWeekStart,
    daysOfWeek,
    goToPreviousWeek,
    goToNextWeek,
    formatDate,
    formatDay,
    generateFairShiftSchedule,
    getEmployeeShift,
    getShiftName,
    getEmployeeName,
    shiftSchedule,
    setShiftSchedule,
    getEmployeeWorkDays,
    getShiftCountsForDay,
    queryEmployee,
    queryListSchedule,
    convertApiDataToShiftSchedule,
    onChangeValueShift,
  };
}
