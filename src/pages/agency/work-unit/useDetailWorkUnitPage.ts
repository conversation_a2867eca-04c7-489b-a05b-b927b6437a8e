import { ENDPOINT } from '@/constants/endpoint';
import ErrorService from '@/services/error.service';
import { HttpService } from '@/services/http.service';
import type { IResDetailAgency } from '@/types/response/IResDetailAgency';
import type { IResDetailWorkUnit } from '@/types/response/IResDetailWorkUnit';
import type { BaseResponse } from '@/types/response/IResModel';
import { useQuery } from '@tanstack/react-query';
import { useParams } from 'react-router-dom';

export function useDetailWorkUnitPage() {
  const httpService = new HttpService();
  const errorService = new ErrorService();
  const { agencyId, workUnitId } = useParams();
  const queryAgency = useQuery({
    queryKey: ['detailAgency', agencyId],
    queryFn: () => {
      return httpService
        .GET(ENDPOINT.DETAIL_AGENCY(agencyId!))
        .then((res: BaseResponse<IResDetailAgency>) => {
          return res.data.response_data;
        })
        .catch((e) => {
          errorService.fetchApiError(e);
          throw e;
        });
    },
    enabled: !!agencyId,
  });

  const queryDetail = useQuery({
    queryKey: ['detailWorkUnit', workUnitId],
    queryFn: () => {
      return httpService
        .GET(ENDPOINT.DETAIL_WORK_UNIT(workUnitId!))
        .then((res: BaseResponse<IResDetailWorkUnit>) => {
          return res.data.response_data;
        })
        .catch((e) => {
          errorService.fetchApiError(e);
          throw e;
        });
    },
    enabled: !!workUnitId,
  });

  return { queryAgency, workUnitId, agencyId, queryDetail };
}
