import { useMutation, useQuery } from '@tanstack/react-query';
import { useNavigate, useParams } from 'react-router-dom';
import { WorkUnitRepository } from '@/repositories/work-unit-repositories.ts';
import { useFormik } from 'formik';
import type { IReqSettingLocation } from '@/types/request/IReqSettingLocation.ts';

export function useSettingWorkUnitLocationPage() {
  const { id } = useParams();
  const workUnitRepository = new WorkUnitRepository();
  const navigate = useNavigate();
  const queryDetail = useQuery({
    queryKey: ['detailWorkUnit', id],
    enabled: !!id,
    queryFn: () => workUnitRepository.getDetailWorkUnit(id!),
  });

  const initState: { data: IReqSettingLocation[]; checked: boolean } = {
    checked: false,
    data: [
      {
        name: '',
        id: '',
        lng: undefined,
        lat: undefined,
        radius: 100,
      },
    ],
  };

  const mutationSubmit = useMutation({
    mutationFn: (e: IReqSettingLocation[]) =>
      workUnitRepository.submitSettingLocation(id, e).then(() => {
        navigate(-1);
      }),
  });

  const formik = useFormik({
    initialValues: initState,
    onSubmit: (e) => {
      mutationSubmit.mutate(e.data);
    },
  });

  const queryLocation = useQuery({
    queryKey: ['locationWorkUnit', id],
    enabled: !!id,
    queryFn: () =>
      workUnitRepository.getLocationWorkUnit(id!).then((res) => {
        if (res.length > 0) {
          formik.setValues({
            checked: false,
            data: res.map((item) => {
              return {
                name: item.name,
                id: item.id,
                lng: item.lng,
                lat: item.lat,
                radius: item.radius,
              };
            }),
          });
          return res;
        } else {
          return [];
        }
      }),
  });

  function onClickAddLocation() {
    const newData = {
      name: '',
      id: '',
      lng: undefined,
      lat: undefined,
      radius: 100,
    };
    formik.setFieldValue('data', [...formik.values.data, newData]);
  }

  return { queryDetail, queryLocation, formik, onClickAddLocation, mutationSubmit };
}
