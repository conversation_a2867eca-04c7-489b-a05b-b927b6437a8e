import CardLoading from '@/components/CardLoading.tsx';
import InputText from '@/components/InputText.tsx';
import PageContainer from '@/components/PageContainer.tsx';
import PageTitle from '@/components/page-title.tsx';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion.tsx';
import { Button } from '@/components/ui/button.tsx';
import { Card, CardContent, CardFooter, CardHeader } from '@/components/ui/card.tsx';
import { Checkbox } from '@/components/ui/checkbox.tsx';
import { Separator } from '@/components/ui/separator.tsx';
import { getInitials } from '@/lib/utils.ts';
import { useSettingScheduleRegularWorkUnitPage } from '@/pages/agency/work-unit/settings/useSettingScheduleRegularWorkUnitPage.ts';
import { ROUTES } from '@/routes/routes.ts';
import type { IBreadcrumbData } from '@/types/type/IBreadcrumbData.ts';
import { FieldArray, FormikProvider } from 'formik';
import { Calendar, Plus, Send } from 'lucide-react';
import { MdClose } from 'react-icons/md';
import { useParams } from 'react-router-dom';

export default function SettingScheduleRegularWorkUnitPage() {
  const { id } = useParams();
  const page = useSettingScheduleRegularWorkUnitPage();
  const data = page.queryDetail?.data;
  const breadcrumbs: IBreadcrumbData[] = [
    {
      label: 'Unit Kerja',
      path: ROUTES.LIST_WORK_UNIT(),
    },
    {
      label: data?.name || '',
      path: ROUTES.DETAIL_WORK_UNIT(id || ''),
    },
    {
      label: 'Kelola jadwal',
    },
  ];

  if (page.queryDetail?.isPending) {
    return (
      <PageContainer>
        <PageTitle title={'Kelola jadwal'} breadcrumb={breadcrumbs} />
        <CardLoading />
      </PageContainer>
    );
  }

  return (
    <PageContainer>
      <PageTitle title={'Kelola jadwal regular'} breadcrumb={breadcrumbs} />
      <Card>
        <CardHeader>
          <div>
            <div className={'flex  gap-3 items-center'}>
              <div
                className={
                  'bg-gradient-to-br from-primary to-blue-700 items-center justify-center p-3 rounded-md text-white'
                }
              >
                <Calendar />
              </div>
              <div>
                <div className={'font-semibold text-2xl '}>Pengaturan jadwal absensi</div>
                <p className={'text-muted-foreground'}>Atur waktu absensi dan toleransi untuk setiap hari kerja</p>
              </div>
            </div>
          </div>
        </CardHeader>
        <Separator />
        <CardContent>
          <FormikProvider value={page.formik}>
            <div className={'grid gap-3'}>
              <Accordion type="single" className="space-y-3" defaultValue={'0'}>
                {page.formik.values.day.map((item, i) => (
                  <AccordionItem value={item.index.toString()} key={i} className={'border-none'}>
                    <AccordionTrigger
                      className={
                        'px-4 py-3 hover:bg-accent/50 transition-colors duration-200 [&[data-state=open]]:bg-accent/70'
                      }
                    >
                      <div className={'flex items-center gap-2 hover'}>
                        <div
                          className={
                            'p-3 bg-gradient-to-br from-primary rounded-full to-blue-700 text-white h-10 w-10 flex items-center justify-center'
                          }
                        >
                          {getInitials(item.label)}
                        </div>
                        <div>
                          <div>{item.label}</div>

                          <p className={'text-sm text-muted-foreground'}>
                            {item.is_holiday ? `Hari libur` : `${item.data.length} jadwal absensi`}
                          </p>
                        </div>
                      </div>
                    </AccordionTrigger>
                    <AccordionContent className="px-4 py-4 bg-muted/30 ">
                      <FieldArray name={`day[${i}].data`}>
                        {({ push, remove }) => (
                          <div className="space-y-3">
                            <Card>
                              <CardContent>
                                <div className={'flex  gap-2'}>
                                  <div className={'pt-1'}>
                                    <Checkbox
                                      checked={item.is_holiday}
                                      onCheckedChange={(e) => page.formik.setFieldValue(`day[${i}].is_holiday`, e)}
                                    />
                                  </div>
                                  <div>
                                    <div className={'font-semibold'}>Ini Hari libur ? </div>
                                    <p className={'text-muted-foreground'}>
                                      Dengan mencentang bahwa ini adalah hari libur, tidak akan ada absensi untuk hari
                                      tersebut
                                    </p>
                                  </div>
                                </div>
                              </CardContent>
                            </Card>
                            <>
                              {item.is_holiday ? (
                                <Card className={'flex items-center justify-center flex-col'}>
                                  <div>Hari Libur</div>
                                </Card>
                              ) : (
                                item.data.map((_dataItem, j) => (
                                  <Card key={j} className={'relative'}>
                                    {item.data.length > 1 && (
                                      <Button
                                        variant="ghost"
                                        size="sm"
                                        type="button"
                                        onClick={() => remove(j)}
                                        className="absolute top-2 right-2 h-7 w-7 p-0 text-destructive hover:text-destructive hover:bg-destructive/10 z-10"
                                      >
                                        <MdClose className="w-3 h-3" />
                                      </Button>
                                    )}

                                    <div className="absolute top-2 left-2 text-xs font-medium px-2 py-0.5 rounded-xs">
                                      Jadwal {j + 1}
                                    </div>

                                    <CardContent className="pt-8 pb-4">
                                      <div className="space-y-4 relative">
                                        {/* Name Input */}
                                        <div className="space-y-2">
                                          <label className="text-sm font-medium text-foreground">Nama Absensi</label>
                                          <InputText
                                            name={`day[${i}].data[${j}].name`}
                                            placeholder="Contoh: Clock In"
                                          />
                                        </div>

                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                          <div className="space-y-2">
                                            <label className="text-sm font-medium text-foreground flex items-center gap-1">
                                              <svg
                                                className="w-3 h-3"
                                                fill="none"
                                                stroke="currentColor"
                                                viewBox="0 0 24 24"
                                              >
                                                <path
                                                  strokeLinecap="round"
                                                  strokeLinejoin="round"
                                                  strokeWidth={2}
                                                  d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                                                />
                                              </svg>
                                              Waktu Absen
                                            </label>
                                            <InputText required type="time" name={`day[${i}].data[${j}].start_time`} />
                                          </div>

                                          <div className="space-y-2">
                                            <label className="text-sm font-medium text-foreground flex items-center gap-1">
                                              <svg
                                                className="w-3 h-3"
                                                fill="none"
                                                stroke="currentColor"
                                                viewBox="0 0 24 24"
                                              >
                                                <path
                                                  strokeLinecap="round"
                                                  strokeLinejoin="round"
                                                  strokeWidth={2}
                                                  d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"
                                                />
                                                <path
                                                  strokeLinecap="round"
                                                  strokeLinejoin="round"
                                                  strokeWidth={2}
                                                  d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                                                />
                                              </svg>
                                              Waktu Toleransi
                                            </label>
                                            <InputText required type="time" name={`day[${i}].data[${j}].end_time`} />
                                          </div>
                                        </div>
                                      </div>
                                    </CardContent>
                                  </Card>
                                ))
                              )}
                            </>
                            {!item.is_holiday && (
                              <div className="flex justify-end pt-2">
                                <Button
                                  type="button"
                                  variant="outline"
                                  onClick={() =>
                                    push({
                                      name: '',
                                      start_time: '',
                                      end_time: '',
                                    })
                                  }
                                >
                                  <Plus className="w-4 h-4 mr-2" />
                                  Tambah Jadwal
                                </Button>
                              </div>
                            )}
                          </div>
                        )}
                      </FieldArray>
                    </AccordionContent>
                  </AccordionItem>
                ))}
              </Accordion>
            </div>
          </FormikProvider>
        </CardContent>
        <Separator />
        <CardFooter>
          <div className={'flex justify-between w-full'}>
            <div className={'flex items-center gap-3'}>
              <Checkbox
                id={'checked'}
                name={'checked'}
                onCheckedChange={(e) => page.formik.setFieldValue('checked', e)}
                checked={page.formik.values.checked}
              />
              <label htmlFor={'checked'}>Konfirmasi perubahan jadwal jadwal</label>
            </div>
            <Button
              loading={page.mutationSettingSchedule.isPending}
              disabled={!page.formik.values.checked}
              onClick={() => page.formik.handleSubmit()}
            >
              Kirim, dan selesaikan
              <Send />
            </Button>
          </div>
        </CardFooter>
      </Card>
    </PageContainer>
  );
}
