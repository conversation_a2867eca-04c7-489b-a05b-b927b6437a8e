import PageTitle from '@/components/page-title';
import PageContainer from '@/components/PageContainer';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Separator } from '@/components/ui/separator';
import { getInitials } from '@/lib/utils';
import { ROUTES } from '@/routes/routes';
import type { IBreadcrumbData } from '@/types/type/IBreadcrumbData';
import {
  Briefcase,
  Building2,
  Calendar,
  ChevronDown,
  Clock,
  Edit,
  Eye,
  FileText,
  MapPin,
  Settings,
  UserPlus,
  Users,
} from 'lucide-react';
import { Link, useNavigate } from 'react-router-dom';
import DetailWorkUnitSkeleton from './DetailWorkUnitSkeleton';
import { useDetailWorkUnitPage } from './useDetailWorkUnitPage';

export default function DetailWorkUnit() {
  const navigate = useNavigate();
  const page = useDetailWorkUnitPage();
  const data = page?.queryDetail?.data;
  const agencyData = page?.queryAgency?.data;

  const breadcrumb: IBreadcrumbData[] = [
    {
      label: 'Instansi',
      path: ROUTES.MASTER_DATA.AGENCY(),
    },
    {
      label: data?.agency_name || '-',
      path: ROUTES.DETAIL_AGENCY(data?.agency_id || ''),
    },
    {
      label: 'Unit Kerja',
      path: ROUTES.DETAIL_AGENCY(page.agencyId || ''),
    },
    {
      label: data?.name || '-',
    },
  ];

  if (page.queryDetail?.isPending) {
    return (
      <PageContainer>
        <PageTitle title="Detail Unit Kerja" breadcrumb={breadcrumb} />
        <DetailWorkUnitSkeleton />
      </PageContainer>
    );
  }

  return (
    <PageContainer>
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6">
        <PageTitle title="Detail Unit Kerja" breadcrumb={breadcrumb} />

        <div className="flex items-center gap-3">
          <Button variant="outline" onClick={() => navigate(ROUTES.DETAIL_AGENCY(page.agencyId || ''))}>
            <Eye className="w-4 h-4 mr-2" />
            Lihat Instansi
          </Button>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button>
                <Settings className="w-4 h-4 mr-2" />
                Kelola
                <ChevronDown className="w-4 h-4 ml-2" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <Link
                to={
                  data?.type === 'SHIFT'
                    ? ROUTES.SETTING_SHIFT_TABLE(data.id)
                    : ROUTES.SETTING_SCHEDULE_REGULAR(data?.id || '')
                }
              >
                <DropdownMenuItem>
                  <Calendar className="w-4 h-4 mr-2" />
                  Atur Jadwal
                </DropdownMenuItem>
              </Link>
              <DropdownMenuItem>
                <UserPlus className="w-4 h-4 mr-2" />
                Tambah Pegawai
              </DropdownMenuItem>
              <Link to={ROUTES.EDIT_WORK_UNIT(data?.id || '')}>
                <DropdownMenuItem>
                  <Edit className="w-4 h-4 mr-2" />
                  Edit Unit Kerja
                </DropdownMenuItem>
              </Link>
              <Link to={ROUTES.SETTING_WORK_UNIT_LOCATION(data?.id || '')}>
                <DropdownMenuItem>
                  <MapPin className="w-4 h-4 mr-2" />
                  Kelola lokasi
                </DropdownMenuItem>
              </Link>
              {data?.type === 'SHIFT' && (
                <Link to={ROUTES.SETTING_SHIFT_WORK_UNIT(data?.id || '')}>
                  <DropdownMenuItem>
                    <Clock className="w-4 h-4 mr-2" />
                    Kelola Shift
                  </DropdownMenuItem>
                </Link>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      <div className="space-y-6 animate-in fade-in-50 duration-500">
        {/* Header Card */}
        <Card className="overflow-hidden transition-all duration-300  py-0">
          <div className="bg-gradient-to-r from-blue-600 to-purple-600 h-32"></div>
          <CardContent className="relative pt-0 pb-6">
            <div className="flex flex-col sm:flex-row gap-6 -mt-16">
              <div className="relative">
                <Avatar className="w-32 h-32 border-4 border-white ">
                  <AvatarImage src={agencyData?.logo_url} alt={data?.name} className="object-cover" />
                  <AvatarFallback className="text-2xl font-bold bg-gradient-to-br from-blue-500 to-purple-600 text-white">
                    {data?.name ? getInitials(data.name) : 'UK'}
                  </AvatarFallback>
                </Avatar>
              </div>

              <div className="flex-1 pt-4 sm:pt-8 min-w-0">
                <div className="flex flex-col pt-4 sm:flex-row sm:items-start sm:justify-between gap-4">
                  <div className="min-w-0 flex-1">
                    <h1 className="text-2xl font-bold text-gray-900 mb-2 break-words">
                      {data?.name || 'Nama Unit Kerja'}
                    </h1>
                    <div className="flex flex-wrap items-center gap-3 text-gray-600 mb-3">
                      <div className="flex items-center gap-2 min-w-0">
                        <Building2 className="w-4 h-4 flex-shrink-0" />
                        <span className="font-medium break-words">{data?.agency_name || agencyData?.name || '-'}</span>
                      </div>
                    </div>
                    {data?.description && (
                      <div className="flex items-start gap-2 min-w-0">
                        <FileText className="w-4 h-4 text-gray-500 flex-shrink-0 mt-0.5" />
                        <p className="text-gray-700 break-words">{data.description}</p>
                      </div>
                    )}
                  </div>

                  <div className="flex flex-col items-start sm:items-end gap-2 flex-shrink-0">
                    <Badge variant="outline" className="flex items-center gap-1">
                      <Briefcase className="w-3 h-3" />
                      Unit Kerja
                    </Badge>
                    <div className="text-sm text-gray-500 break-all">ID: {data?.id || '-'}</div>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Information Cards */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Basic Information */}
          <Card className="transition-all duration-300  ">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Briefcase className="w-5 h-5" />
                Informasi Dasar
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 py-2">
                <div className="flex items-center gap-3 min-w-0">
                  <FileText className="w-4 h-4 text-gray-500 flex-shrink-0" />
                  <span className="text-sm font-medium text-gray-600">Nama Unit Kerja</span>
                </div>
                <span className="text-sm text-gray-900 break-words sm:text-right sm:max-w-[60%]">
                  {data?.name || '-'}
                </span>
              </div>
              <Separator />
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 py-2">
                <div className="flex items-center gap-3 min-w-0">
                  <Building2 className="w-4 h-4 text-gray-500 flex-shrink-0" />
                  <span className="text-sm font-medium text-gray-600">Instansi</span>
                </div>
                <span className="text-sm text-gray-900 break-words sm:text-right sm:max-w-[60%]">
                  {data?.agency_name || agencyData?.name || '-'}
                </span>
              </div>
              <Separator />
              <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-2 py-2">
                <div className="flex items-center gap-3 min-w-0">
                  <FileText className="w-4 h-4 text-gray-500 flex-shrink-0" />
                  <span className="text-sm font-medium text-gray-600">Deskripsi</span>
                </div>
                <span className="text-sm text-gray-900 break-words sm:text-right sm:max-w-[60%]">
                  {data?.description || 'Tidak ada deskripsi'}
                </span>
              </div>
            </CardContent>
          </Card>

          {/* Statistics */}
          <Card className="transition-all duration-300  ">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="w-5 h-5" />
                Statistik
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 py-2">
                <div className="flex items-center gap-3 min-w-0">
                  <Users className="w-4 h-4 text-gray-500 flex-shrink-0" />
                  <span className="text-sm font-medium text-gray-600">Total Pegawai</span>
                </div>
                <Badge variant="secondary" className="self-start sm:self-center">
                  0 Pegawai
                </Badge>
              </div>
              <Separator />
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 py-2">
                <div className="flex items-center gap-3 min-w-0">
                  <MapPin className="w-4 h-4 text-gray-500 flex-shrink-0" />
                  <span className="text-sm font-medium text-gray-600">Lokasi Kerja</span>
                </div>
                <Badge variant="secondary" className="self-start sm:self-center">
                  0 Lokasi
                </Badge>
              </div>
              <Separator />
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 py-2">
                <div className="flex items-center gap-3 min-w-0">
                  <Calendar className="w-4 h-4 text-gray-500 flex-shrink-0" />
                  <span className="text-sm font-medium text-gray-600">Jadwal Kerja</span>
                </div>
                <Badge variant="secondary" className="self-start sm:self-center">
                  Belum Diatur
                </Badge>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Quick Actions */}
        <Card className="transition-all duration-300  ">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="w-5 h-5" />
              Aksi Cepat
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
              <Button
                variant="outline"
                className="flex items-center gap-3 h-auto py-4 px-4 transition-all duration-200   hover:border-blue-300 min-w-0"
                onClick={() =>
                  navigate(
                    data?.type === 'SHIFT'
                      ? ROUTES.SETTING_SHIFT_TABLE(data.id)
                      : ROUTES.SETTING_SCHEDULE_REGULAR(data?.id || ''),
                  )
                }
              >
                <Calendar className="w-5 h-5 flex-shrink-0" />
                <div className="text-left min-w-0">
                  <div className="font-medium break-words">Atur Jadwal</div>
                  <div className="text-xs text-gray-500 break-words">Kelola jadwal kerja</div>
                </div>
              </Button>

              <Button
                variant="outline"
                className="flex items-center gap-3 h-auto py-4 px-4 transition-all duration-200  hover:-translate-y-0.5 hover:border-green-300 min-w-0"
              >
                <UserPlus className="w-5 h-5 flex-shrink-0" />
                <div className="text-left min-w-0">
                  <div className="font-medium break-words">Tambah Pegawai</div>
                  <div className="text-xs text-gray-500 break-words">Daftarkan pegawai baru</div>
                </div>
              </Button>

              <Link to={ROUTES.SETTING_WORK_UNIT_LOCATION(page?.workUnitId || '')} className={'w-full'}>
                <Button
                  variant="outline"
                  className="flex items-center w-full gap-3 h-auto py-4 px-4 transition-all duration-200  hover:-translate-y-0.5 hover:border-purple-300 min-w-0"
                >
                  <MapPin className="w-5 h-5 flex-shrink-0" />
                  <div className="text-left min-w-0">
                    <div className="font-medium break-words">Kelola Lokasi</div>
                    <div className="text-xs text-gray-500 break-words">Atur lokasi kerja</div>
                  </div>
                </Button>
              </Link>

              <Button
                variant="outline"
                className="flex items-center gap-3 h-auto py-4 px-4 transition-all duration-200  hover:-translate-y-0.5 hover:border-orange-300 min-w-0"
              >
                <Edit className="w-5 h-5 flex-shrink-0" />
                <div className="text-left min-w-0">
                  <div className="font-medium break-words">Edit Unit Kerja</div>
                  <div className="text-xs text-gray-500 break-words">Ubah informasi unit</div>
                </div>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </PageContainer>
  );
}
