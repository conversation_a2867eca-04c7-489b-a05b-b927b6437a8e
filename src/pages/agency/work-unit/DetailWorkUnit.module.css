/* Detail Work Unit Page Styles */

.profileHeader {
  background: linear-gradient(135deg, #2563eb 0%, #7c3aed 100%);
  position: relative;
  overflow: hidden;
}

.profileHeader::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.3;
}

.avatarContainer {
  position: relative;
  z-index: 10;
}

.workUnitBadge {
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.infoCard {
  transition: all 0.3s ease;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.infoCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.actionButton {
  transition: all 0.2s ease;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.actionButton:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.gradientText {
  background: linear-gradient(135deg, #2563eb 0%, #7c3aed 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.dataRow {
  transition: background-color 0.2s ease;
}

.dataRow:hover {
  background-color: rgba(0, 0, 0, 0.02);
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .profileHeader {
    height: 120px;
  }
  
  .avatarContainer {
    margin-top: -60px;
  }
}

/* Prevent overflow and ensure proper text wrapping */
.textContainer {
  min-width: 0;
  word-wrap: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
}

.flexContainer {
  min-width: 0;
  flex: 1;
}

.iconContainer {
  flex-shrink: 0;
}

/* Ensure proper spacing on mobile */
@media (max-width: 640px) {
  .mobileStack {
    flex-direction: column;
    align-items: flex-start !important;
  }
  
  .mobileFullWidth {
    width: 100%;
  }
}

/* Animation for loading states */
.skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Statistics badge animations */
.statisticsBadge {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

/* Card entrance animation */
.cardEnter {
  animation: slideInUp 0.6s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Action button specific hover effects */
.scheduleButton:hover {
  border-color: #3b82f6;
  background-color: rgba(59, 130, 246, 0.05);
}

.employeeButton:hover {
  border-color: #10b981;
  background-color: rgba(16, 185, 129, 0.05);
}

.locationButton:hover {
  border-color: #8b5cf6;
  background-color: rgba(139, 92, 246, 0.05);
}

.editButton:hover {
  border-color: #f59e0b;
  background-color: rgba(245, 158, 11, 0.05);
}

/* Header action buttons */
.headerActions {
  display: flex;
  gap: 0.75rem;
  align-items: center;
}

@media (max-width: 640px) {
  .headerActions {
    flex-direction: column;
    width: 100%;
  }
  
  .headerActions button {
    width: 100%;
  }
}

/* Work unit specific styling */
.workUnitHeader {
  background: linear-gradient(135deg, #1e40af 0%, #7c2d12 100%);
}

.workUnitIcon {
  color: #3b82f6;
}

.statisticsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

/* Enhanced hover effects for statistics */
.statisticItem {
  transition: all 0.2s ease;
  padding: 0.5rem;
  border-radius: 0.5rem;
}

.statisticItem:hover {
  background-color: rgba(59, 130, 246, 0.05);
  transform: translateX(2px);
}

/* Loading state improvements */
.loadingContainer {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.loadingCard {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 0.5rem;
  padding: 1.5rem;
}

/* Accessibility improvements */
.focusVisible:focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Print styles */
@media print {
  .actionButton,
  .headerActions {
    display: none;
  }
  
  .infoCard {
    box-shadow: none;
    border: 1px solid #000;
  }
}
