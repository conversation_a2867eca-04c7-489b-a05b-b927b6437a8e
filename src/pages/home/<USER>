import PageContainer from '@/components/PageContainer';
import DemoValidateUser from '../demo-validate-user/DemoValidateUserPage';
import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useAuth } from '@/hooks/use-auth';
import {
  Users,
  Building2,
  Briefcase,
  TrendingUp,
  Clock,
  CheckCircle,
  Camera,
  BarChart3,
  Activity,
  UserCheck,
  UserX,
  Eye,
  EyeOff,
} from 'lucide-react';

function getGreeting(): string {
  const hour = new Date().getHours();
  if (hour < 12) return 'Selamat Pagi';
  if (hour < 15) return 'Selamat Siang';
  if (hour < 18) return 'Selamat Sore';
  return 'Selamat Malam';
}

function formatDate(date: Date): string {
  return date.toLocaleDateString('id-ID', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });
}

export default function HomePage() {
  const [showDemo, setShowDemo] = useState<boolean>(false);
  const auth = useAuth();
  const currentDate = new Date();

  // Mock data - in real app, this would come from API
  const dashboardStats = {
    totalEmployees: 1247,
    totalAgencies: 15,
    totalWorkUnits: 89,
    todayAttendance: 892,
    attendanceRate: 71.6,
    onTimeRate: 85.2,
    lateEmployees: 45,
    absentEmployees: 310,
  };

  const recentActivities = [
    {
      id: 1,
      type: 'attendance',
      message: 'Ahmad Budi melakukan absensi masuk',
      time: '2 menit lalu',
      icon: UserCheck,
      color: 'text-green-600',
    },
    {
      id: 2,
      type: 'employee',
      message: 'Pegawai baru Siti Nurhaliza didaftarkan',
      time: '15 menit lalu',
      icon: Users,
      color: 'text-blue-600',
    },
    {
      id: 3,
      type: 'late',
      message: '12 pegawai terlambat hari ini',
      time: '1 jam lalu',
      icon: Clock,
      color: 'text-orange-600',
    },
    {
      id: 4,
      type: 'agency',
      message: 'Unit Kerja IT ditambahkan ke Dinas Kominfo',
      time: '2 jam lalu',
      icon: Building2,
      color: 'text-purple-600',
    },
  ];

  return (
    <div className="space-y-6">
      <PageContainer>
        {/* Welcome Section */}
        <div className="mb-8">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                {getGreeting()}, {auth?.user?.name?.split(' ')[0] || 'Admin'}! 👋
              </h1>
              <p className="text-gray-600 mt-1">
                {formatDate(currentDate)} • {auth?.user?.agency_name || 'Dashboard Admin'}
              </p>
            </div>
            <div className="flex items-center gap-3">
              <Button
                variant={showDemo ? 'destructive' : 'default'}
                onClick={() => setShowDemo(!showDemo)}
                className="flex items-center gap-2"
              >
                {showDemo ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                {showDemo ? 'Sembunyikan Demo' : 'Demo Validasi Wajah'}
              </Button>
            </div>
          </div>
        </div>

        {/* Demo Section */}
        {showDemo && (
          <div className="mb-8">
            <Card className="border-blue-200 bg-blue-50/50">
              <CardHeader>
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                    <Camera className="w-5 h-5 text-blue-600" />
                  </div>
                  <div>
                    <CardTitle className="text-lg text-blue-900">Demo Validasi Wajah</CardTitle>
                    <p className="text-sm text-blue-700 mt-1">Uji coba sistem validasi wajah untuk absensi pegawai</p>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <DemoValidateUser />
              </CardContent>
            </Card>
          </div>
        )}

        {/* Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card className="border-blue-200 bg-gradient-to-br from-blue-50 to-blue-100/50">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-blue-700">Total Pegawai</p>
                  <p className="text-2xl font-bold text-blue-900">{dashboardStats.totalEmployees.toLocaleString()}</p>
                  <p className="text-xs text-blue-600 mt-1">Terdaftar di sistem</p>
                </div>
                <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                  <Users className="w-6 h-6 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-green-200 bg-gradient-to-br from-green-50 to-green-100/50">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-green-700">Absensi Hari Ini</p>
                  <p className="text-2xl font-bold text-green-900">{dashboardStats.todayAttendance.toLocaleString()}</p>
                  <div className="flex items-center gap-1 mt-1">
                    <TrendingUp className="w-3 h-3 text-green-600" />
                    <p className="text-xs text-green-600">{dashboardStats.attendanceRate}% hadir</p>
                  </div>
                </div>
                <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                  <UserCheck className="w-6 h-6 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-orange-200 bg-gradient-to-br from-orange-50 to-orange-100/50">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-orange-700">Keterlambatan</p>
                  <p className="text-2xl font-bold text-orange-900">{dashboardStats.lateEmployees}</p>
                  <p className="text-xs text-orange-600 mt-1">Pegawai terlambat</p>
                </div>
                <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                  <Clock className="w-6 h-6 text-orange-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-purple-200 bg-gradient-to-br from-purple-50 to-purple-100/50">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-purple-700">Total Instansi</p>
                  <p className="text-2xl font-bold text-purple-900">{dashboardStats.totalAgencies}</p>
                  <p className="text-xs text-purple-600 mt-1">{dashboardStats.totalWorkUnits} unit kerja</p>
                </div>
                <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                  <Building2 className="w-6 h-6 text-purple-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Attendance Overview */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                      <BarChart3 className="w-5 h-5 text-blue-600" />
                    </div>
                    <div>
                      <CardTitle>Ringkasan Absensi Hari Ini</CardTitle>
                      <p className="text-sm text-gray-600 mt-1">Status kehadiran pegawai</p>
                    </div>
                  </div>
                  <Badge variant="outline" className="text-xs">
                    Live
                  </Badge>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                  <div className="bg-green-50 rounded-lg p-4 border border-green-200">
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                        <CheckCircle className="w-4 h-4 text-green-600" />
                      </div>
                      <div>
                        <p className="text-lg font-bold text-green-900">{dashboardStats.todayAttendance}</p>
                        <p className="text-sm text-green-700">Hadir</p>
                      </div>
                    </div>
                  </div>

                  <div className="bg-orange-50 rounded-lg p-4 border border-orange-200">
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center">
                        <Clock className="w-4 h-4 text-orange-600" />
                      </div>
                      <div>
                        <p className="text-lg font-bold text-orange-900">{dashboardStats.lateEmployees}</p>
                        <p className="text-sm text-orange-700">Terlambat</p>
                      </div>
                    </div>
                  </div>

                  <div className="bg-red-50 rounded-lg p-4 border border-red-200">
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center">
                        <UserX className="w-4 h-4 text-red-600" />
                      </div>
                      <div>
                        <p className="text-lg font-bold text-red-900">{dashboardStats.absentEmployees}</p>
                        <p className="text-sm text-red-700">Tidak Hadir</p>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="mt-6">
                  <div className="flex items-center justify-between text-sm text-gray-600 mb-2">
                    <span>Tingkat Kehadiran</span>
                    <span>{dashboardStats.attendanceRate}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-green-500 h-2 rounded-full transition-all duration-500"
                      style={{ width: `${dashboardStats.attendanceRate}%` }}
                    ></div>
                  </div>
                </div>

                <div className="mt-4">
                  <div className="flex items-center justify-between text-sm text-gray-600 mb-2">
                    <span>Tingkat Ketepatan Waktu</span>
                    <span>{dashboardStats.onTimeRate}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-blue-500 h-2 rounded-full transition-all duration-500"
                      style={{ width: `${dashboardStats.onTimeRate}%` }}
                    ></div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Recent Activities */}
          <div>
            <Card>
              <CardHeader>
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                    <Activity className="w-5 h-5 text-green-600" />
                  </div>
                  <div>
                    <CardTitle>Aktivitas Terbaru</CardTitle>
                    <p className="text-sm text-gray-600 mt-1">Update sistem real-time</p>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {recentActivities.map((activity) => {
                    const IconComponent = activity.icon;
                    return (
                      <div key={activity.id} className="flex items-start gap-3 p-3 bg-gray-50 rounded-lg">
                        <div className="w-8 h-8 bg-white rounded-lg flex items-center justify-center border">
                          <IconComponent className={`w-4 h-4 ${activity.color}`} />
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium text-gray-900">{activity.message}</p>
                          <p className="text-xs text-gray-500 mt-1">{activity.time}</p>
                        </div>
                      </div>
                    );
                  })}
                </div>
                <Button variant="outline" className="w-full mt-4" size="sm">
                  Lihat Semua Aktivitas
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="mt-8">
          <Card>
            <CardHeader>
              <CardTitle>Aksi Cepat</CardTitle>
              <p className="text-sm text-gray-600">Akses fitur utama dengan cepat</p>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <Button variant="outline" className="h-20 flex flex-col gap-2">
                  <Users className="w-5 h-5" />
                  <span className="text-sm">Tambah Pegawai</span>
                </Button>
                <Button variant="outline" className="h-20 flex flex-col gap-2">
                  <Building2 className="w-5 h-5" />
                  <span className="text-sm">Tambah Instansi</span>
                </Button>
                <Button variant="outline" className="h-20 flex flex-col gap-2">
                  <Briefcase className="w-5 h-5" />
                  <span className="text-sm">Unit Kerja</span>
                </Button>
                <Button variant="outline" className="h-20 flex flex-col gap-2">
                  <BarChart3 className="w-5 h-5" />
                  <span className="text-sm">Laporan</span>
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </PageContainer>
    </div>
  );
}
