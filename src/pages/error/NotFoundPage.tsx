import { Button } from '@/components/ui/button.tsx';
import { Link } from 'react-router-dom';
import { useAuth } from '@/hooks/use-auth.ts';
import { ROUTES } from '@/routes/routes.ts';

export default function NotFoundPage() {
  const auth = useAuth();
  return (
    <div className={'h-screen w-full flex items-center justify-center'}>
      <div className={'flex flex-col gap-10 items-center'}>
        <div className={'text-3xl font-semibold'}><PERSON><PERSON>, halaman yang anda cari tidak ditemukan</div>
        <Link to={auth.token ? ROUTES.HOME() : ROUTES.SIGN_IN()} className={'w-fit'}>
          <Button>Kembali Ke beranda</Button>
        </Link>
      </div>
    </div>
  );
}
