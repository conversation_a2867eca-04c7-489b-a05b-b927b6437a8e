'use client';

import Label from '@/components/ui/Label';
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem } from '@/components/ui/command';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { cn } from '@/lib/utils';
import type { ILabelValue } from '@/types/type/ILabelValue';
import { type FormikErrors, type FormikTouched, getIn, useFormikContext } from 'formik';
import type { ReactNode } from 'react';
import { useState } from 'react';

interface IProps<T = any> {
  id: string;
  label?: string;
  required?: boolean;
  placeholder?: string;
  startIcon?: ReactNode;
  endIcon?: ReactNode;
  errorMessage?: any;
  helperText?: string;
  name: string;
  value?: T;
  onValueChange?: (value: T) => void;
  options: ILabelValue<T>[];
  disabled?: boolean;
  dataTestId?: string;
  disableFormik?: boolean;
}

export default function InputAutocomplete<T = string>(props: IProps<T>) {
  const formik = !props.disableFormik ? useFormikContext<any>() : undefined;

  const errors = formik?.errors as FormikErrors<Record<string, any>>;
  const touched = formik?.touched as FormikTouched<Record<string, any>>;

  const errorMessage =
    props.errorMessage ?? (getIn(touched, props.name) && getIn(errors, props.name) ? getIn(errors, props.name) : '');

  const currentValue = props.value ?? getIn(formik?.values, props.name) ?? '';

  const selectedOption = props.options.find((opt) => String(opt.value) === String(currentValue));
  const selectedLabel = selectedOption?.label ?? '';

  const [open, setOpen] = useState(false);

  const handleSelect = (value: string) => {
    const option = props.options.find((o) => String(o.label).toLowerCase() === value.toLowerCase());
    const actualValue = option ? option.value : value;

    if (props.onValueChange) {
      props.onValueChange(actualValue as T);
    }

    if (formik) {
      formik.setFieldValue(props.name, actualValue);
    }

    setOpen(false);
  };

  return (
    <div className="grid w-full">
      {props.label && <Label label={props.label} required={props.required} />}
      <div className={cn('relative flex items-center')}>
        {props.startIcon && (
          <span className="absolute text-gray-500 left-3 flex items-center pr-3 z-10">{props.startIcon}</span>
        )}
        <Popover open={open} onOpenChange={setOpen}>
          <PopoverTrigger asChild disabled={props.disabled}>
            <div
              data-testid={props.dataTestId}
              className={cn(
                'w-full cursor-pointer border rounded-md px-3 py-2 text-sm',
                props.startIcon ? 'pl-12' : '',
                props.endIcon ? 'pr-9' : '',
                errorMessage ? 'outline-red-500 border-red-500 bg-red-100' : '',
                'dark:bg-card bg-white',
              )}
            >
              {selectedLabel || <span className="text-muted-foreground">{props.placeholder || 'Pilih opsi...'}</span>}
            </div>
          </PopoverTrigger>
          <PopoverContent className="p-0 w-full" align="start" side="bottom">
            <Command className="w-full">
              <CommandInput placeholder="Cari..." className="h-9" />
              <CommandEmpty>Opsi tidak ditemukan.</CommandEmpty>
              <CommandGroup className="max-h-[400px] overflow-y-auto w-full">
                {props.options.map((option, index) => (
                  <CommandItem className="cursor-pointer" key={index} onSelect={() => handleSelect(option.label)}>
                    {option.label}
                  </CommandItem>
                ))}
              </CommandGroup>
            </Command>
          </PopoverContent>
        </Popover>

        {props.endIcon && (
          <span className="absolute text-gray-500 right-3 flex items-center pl-3 z-10">{props.endIcon}</span>
        )}
      </div>

      {(errorMessage || props.helperText) && (
        <p className={cn('text-xs mt-1', errorMessage ? 'text-red-500' : 'text-gray-500')}>
          {errorMessage || props.helperText}
        </p>
      )}
    </div>
  );
}
