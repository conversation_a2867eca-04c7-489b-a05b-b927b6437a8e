import { createContext, type ReactNode, useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';

import { ENDPOINT } from '@/constants/endpoint.ts';
import { LOCAL_STORAGE_KEY } from '@/constants/local-storage-key.ts';
import { useUi } from '@/hooks/useUi.ts';
import { ROUTES } from '@/routes/routes.ts';
import ErrorService from '@/services/error.service.ts';
import { HttpService } from '@/services/http.service.ts';
import type { IReqSignIn } from '@/types/request/IReqSignIn.ts';
import type { IResGetMe } from '@/types/response/IResGetMe.ts';
import type { BaseResponse } from '@/types/response/IResModel.ts';
import type { IResSignIn } from '@/types/response/IResSignIn.ts';

const AuthProvider = ({ children }: { children: ReactNode }) => {
  const rawUser = localStorage.getItem(LOCAL_STORAGE_KEY.USER);
  const getToken = localStorage.getItem(LOCAL_STORAGE_KEY.ACCESS_TOKEN);
  const getUser = rawUser ? JSON.parse(rawUser) : undefined;
  const [token, setToken] = useState<string | undefined>(getToken || undefined);
  const [user, setUser] = useState<IResGetMe | undefined>(getUser || undefined);
  const { toast } = useUi();
  const navigate = useNavigate();
  const httpService = new HttpService();
  const errorService = new ErrorService();
  const location = useLocation();

  useEffect(() => {
    if (location.pathname !== ROUTES.SIGN_IN()) {
      httpService
        .GET(ENDPOINT.GET_ME())
        .then((res: BaseResponse<IResGetMe>) => {
          if (res?.data?.response_data) {
            setUser(res.data.response_data);
            localStorage.setItem(LOCAL_STORAGE_KEY.USER, JSON.stringify(res.data.response_data));
          }
        })
        .catch((e) => {
          errorService.fetchApiError(e);
        });
    }
  }, []);

  function loginAction(data: IReqSignIn, setLoading: (loading: boolean) => void) {
    setLoading(true);
    httpService
      .POST(ENDPOINT.SIGN_IN(), data)
      .then((res: BaseResponse<IResSignIn>) => {
        setLoading(false);
        const resToken = res.data.response_data.access_token;
        const userData = res.data.response_data.account_data;
        setToken(resToken);
        setUser(userData);
        localStorage.setItem(LOCAL_STORAGE_KEY.ACCESS_TOKEN, resToken);
        localStorage.setItem(LOCAL_STORAGE_KEY.USER, JSON.stringify(userData));
        toast.success('Sign In Successfully');
        navigate(ROUTES.HOME());
      })
      .catch((e) => {
        errorService.fetchApiError(e);
        setLoading(false);
      });
  }

  const logOut = () => {
    localStorage.clear();
    if (location?.pathname !== ROUTES.SIGN_IN()) {
      window.location.replace(ROUTES.SIGN_IN());
    }
  };

  return <AuthContext value={{ token, loginAction, logOut, user }}>{children}</AuthContext>;
};

export default AuthProvider;

export interface IAuthProviderProps {
  user?: IResGetMe;
  token?: string;
  loginAction: (data: IReqSignIn, setLoading: (data: boolean) => void) => void;
  logOut: () => void;
}

export const AuthContext = createContext<IAuthProviderProps>({
  loginAction: () => {},
  token: undefined,
  logOut: () => {},
  user: undefined,
});
