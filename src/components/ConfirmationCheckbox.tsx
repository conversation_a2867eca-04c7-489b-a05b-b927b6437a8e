import { Checkbox } from './ui/checkbox';

export default function ConfirmationCheckBox(props: IProps) {
  return (
    <div className="grid gap-0 ">
      <div className="flex items-center gap-3  cursor-pointer">
        <Checkbox
          className="bg-white"
          checked={props.checked || false}
          onCheckedChange={props.onCheckedChange}
          id={props?.name || 'checked'}
          name={props?.name || 'checked'}
        />
        <label className="font-semibold " htmlFor={props?.name || 'checked'}>
          {props.label || 'Konfirmasi'}
        </label>
      </div>

      <label htmlFor={props?.name || 'checked'} className="pl-7  cursor-pointer  text-sm">
        {props.description && <p>{props.description}</p>}
      </label>
    </div>
  );
}

interface IProps {
  description?: string;
  label: string;
  name?: string;
  checked?: boolean;
  onCheckedChange: (e: boolean) => void;
}
