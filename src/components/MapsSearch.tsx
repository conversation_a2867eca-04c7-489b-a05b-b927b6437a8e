import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, use<PERSON>ap, useMapE<PERSON>s, Circle } from 'react-leaflet';
import { useEffect, useState } from 'react';
import L from 'leaflet';
import 'leaflet/dist/leaflet.css';
import { useTheme } from '@/components/providers/theme-provider.tsx';
import { MdSearch } from 'react-icons/md';
import {
  CommandDialog,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator,
} from '@/components/ui/command.tsx';
import { Calendar } from 'lucide-react';
import axios from 'axios';
import { useUi } from '@/hooks/useUi.ts';
import { CommandLoading } from 'cmdk';
import { ASSETS } from '@/constants/assets.ts';

export const markerIcon = new L.Icon({
  iconUrl: ASSETS.IMG_MARKER,
  iconSize: [25, 41],
  iconAnchor: [12, 41],
});

const defaultZoom = 20;

export default function MapsSearch(props: IProps) {
  const [loadingFetching, setLoadingFetching] = useState(false);
  const [searchResults, setSearchResults] = useState<any[]>([]);
  const [position, setPosition] = useState<[number, number] | null>(null);
  const [showDialog, setShowDialog] = useState<boolean>(false);
  const [query, setQuery] = useState<string | undefined>(undefined);
  const { theme } = useTheme();
  const { toast } = useUi();
  const [load, setLoad] = useState(false);

  useEffect(() => {
    setLoad(true);
  }, []);

  useEffect(() => {
    if (props.value?.lat && props.value?.lng && props.value?.lat) {
      setPosition([props?.value?.lat || 0, props?.value?.lng || 0]);
    }
  }, [props.value]);

  useEffect(() => {
    navigator.geolocation.getCurrentPosition(
      (pos) => {
        const { latitude, longitude } = pos.coords;
        setPosition([latitude, longitude]);
      },
      (err) => {
        console.error('Could not get location', err);
        setPosition([-6.2, 106.8]);
      },
    );
  }, []);

  useEffect(() => {
    if (!query) return;
    const handler = setTimeout(() => {
      setLoadingFetching(true);
      setSearchResults([]);
      const fetchLocation = async () => {
        try {
          const response = await axios.get(
            `https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(query)}`,
          );
          const results = await response.data;
          setLoadingFetching(false);
          if (results.length > 0) {
            const { lat, lon } = results[0];
            setPosition([parseFloat(lat), parseFloat(lon)]);
            setSearchResults(results);
          } else {
            toast.error('Lokasi tidak ditemukan');
          }
        } catch (e) {
          console.error('Error getting location', e);
          setLoadingFetching(false);
          setSearchResults([]);
          toast.error('Error fetching location:');
        }
      };

      fetchLocation().then();
    }, 500);

    return () => clearTimeout(handler);
  }, [query]);

  if (!position && !load) return <p>Loading map...</p>;

  return (
    <>
      <CommandDialog open={showDialog} onOpenChange={setShowDialog}>
        <CommandInput loading={loadingFetching} onValueChange={(e) => setQuery(e)} placeholder="Cari lokasi..." />
        <CommandList>
          {loadingFetching ? (
            <CommandLoading>
              <CommandItem>
                <Calendar />
                <span>Calendar</span>
              </CommandItem>
            </CommandLoading>
          ) : query && searchResults.length === 0 ? (
            <CommandEmpty>No results found.</CommandEmpty>
          ) : query && searchResults.length > 0 ? (
            <>
              <CommandGroup heading="Search Results">
                {searchResults.map((result, idx) => (
                  <CommandItem
                    key={idx}
                    onSelect={() => {
                      setPosition([parseFloat(result.lat), parseFloat(result.lon)]);
                      setShowDialog(false);
                      props.onChange({
                        lat: parseFloat(result.lat),
                        lng: parseFloat(result.lon),
                      });
                    }}
                  >
                    {result.display_name}
                  </CommandItem>
                ))}
              </CommandGroup>
              <CommandSeparator />
            </>
          ) : (
            <></>
          )}
        </CommandList>
      </CommandDialog>
      <div className={'flex flex-col gap-3'}>
        <div
          onClick={() => setShowDialog(true)}
          className={'border py-2 px-3 bg-white dark:bg-background flex gap-3 items-center rounded-md cursor-pointer'}
        >
          <MdSearch />
          <div className={'text-gray-500'}>Cari Lokasi...</div>
        </div>
        <MapContainer
          center={position || [0, 0]}
          zoom={defaultZoom}
          style={{ height: '400px', width: '100%', zIndex: 0 }}
          className="z-0 "
        >
          {theme === 'dark' ? (
            <TileLayer
              attribution='&copy; <a href="https://carto.com/attributions">CARTO</a>'
              url="https://{s}.basemaps.cartocdn.com/dark_all/{z}/{x}/{y}{r}.png"
            />
          ) : (
            <TileLayer
              attribution="&copy; OpenStreetMap contributors"
              url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
            />
          )}

          {position && <Marker position={position} icon={markerIcon} />}
          {position && props.radius && (
            <Circle
              center={position}
              radius={props.radius}
              pathOptions={{
                color: props.radiusColor || '#3388ff',
                fillColor: props.radiusColor || '#3388ff',
                fillOpacity: 0.2,
                weight: 2,
              }}
            />
          )}
          {position && <ChangeMapView coords={position} />}
          <ClickHandler
            onClick={(latlng) => {
              props.onChange({
                lat: latlng.lat,
                lng: latlng.lng,
              });
              setPosition([latlng.lat, latlng.lng]);
            }}
          />
        </MapContainer>
      </div>
    </>
  );
}

function ClickHandler({ onClick }: { onClick: (latlng: L.LatLng) => void }) {
  useMapEvents({
    click: (e) => onClick(e.latlng),
  });
  return null;
}

function ChangeMapView({ coords }: { coords: [number, number] }) {
  const map = useMap();
  useEffect(() => {
    map.setView(coords, defaultZoom);
  }, [coords, map]);
  return null;
}

interface IProps {
  onChange: ({ lat, lng }: { lat?: number; lng?: number }) => void;
  radius?: number;
  radiusColor?: string;
  value?: {
    lat?: number;
    lng?: number;
  };
}
