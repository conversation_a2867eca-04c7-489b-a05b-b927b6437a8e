import { useAuth } from '@/hooks/use-auth';
import { privateRoutes, publicRoutes } from '@/routes/routes-list';
import { Route, Routes } from 'react-router-dom';
import Base from './Base';
import NotFoundPage from '@/pages/error/NotFoundPage.tsx';

export default function RoutesBuilder() {
  const auth = useAuth();
  const list = auth.user ? privateRoutes : publicRoutes;
  return (
    <Routes>
      {[...list].map((route) => {
        const Element = route.element;
        return (
          <Route
            key={route.path}
            path={route.path}
            element={
              <Base type={route.type}>
                <Element />
              </Base>
            }
          />
        );
      })}
      <Route
        element={
          <Base type={'FULL_PAGE'}>
            <NotFoundPage />
          </Base>
        }
        path={'*'}
      />
    </Routes>
  );
}
