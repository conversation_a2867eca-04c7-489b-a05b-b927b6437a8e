import type { FocusEventHandler, ReactNode } from 'react';
import { cn } from '@/lib/utils.ts';
import Label from '@/components/ui/Label.tsx';
import { type FormikErrors, type FormikTouched, getIn, useFormikContext } from 'formik';
import { Input } from '@/components/ui/input.tsx';
import { Clock } from 'lucide-react';
import { useEffect, useRef } from 'react';

interface IProps {
  id?: string;
  label?: string;
  required?: boolean;
  placeholder?: string;
  startIcon?: ReactNode;
  endIcon?: ReactNode;
  onEnter?: () => void;
  errorMessage?: any;
  helperText?: string;
  name: string;
  value?: string;
  onBlur?: FocusEventHandler<HTMLInputElement>;
  onChange?: (value: string) => void;
  autoComplete?: string;
  dataTestId?: string;
  format?: '12' | '24'; // Format 12 jam atau 24 jam
  step?: number; // Step untuk menit (default: 1)
}

export default function InputTime(props: IProps) {
  const formik = useFormikContext<any>();
  const inputRef = useRef<HTMLInputElement>(null);

  const errors = formik?.errors as FormikErrors<Record<string, any>>;
  const touched = formik?.touched as FormikTouched<Record<string, any>>;

  const errorMessage =
    props.errorMessage ?? (getIn(touched, props.name) && getIn(errors, props.name) ? getIn(errors, props.name) : '');

  // Force 24-hour format
  useEffect(() => {
    if (inputRef.current) {
      // Set the input to always use 24-hour format
      inputRef.current.style.setProperty('--webkit-appearance', 'none');
      inputRef.current.style.setProperty('-moz-appearance', 'textfield');

      // Force locale to use 24-hour format
      try {
        // Check if browser supports changing the locale behavior
        const isTime24 =
          new Intl.DateTimeFormat('en-GB', {
            hour: 'numeric',
            hour12: false,
          }).resolvedOptions().hour12 === false;

        if (!isTime24) {
          // Add data attribute to help with CSS targeting
          inputRef.current.setAttribute('data-format', '24');
        }
      } catch (e) {
        // Fallback: just set the attribute
        inputRef.current.setAttribute('data-format', '24');
      }
    }
  }, []);

  const handleTimeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const timeValue = e.target.value;

    if (timeValue && timeValue.match(/^\d{2}:\d{2}$/)) {
      if (props.onChange) {
        props.onChange(timeValue);
      } else if (formik?.setFieldValue) {
        formik.setFieldValue(props.name, timeValue);
      }
    }
  };

  const currentValue = props.value ?? getIn(formik?.values, props.name) ?? '';

  return (
    <div className="grid">
      {props.label && <Label label={props.label} required={props.required} />}
      <div className={cn('relative flex items-center dark:bg-card bg-white')}>
        {props.startIcon && (
          <span className="absolute text-gray-500 left-3 flex items-center pr-3">{props.startIcon}</span>
        )}

        <Input
          ref={inputRef}
          data-testid={props.dataTestId}
          autoComplete={props.autoComplete}
          onBlur={props.onBlur ?? formik?.handleBlur}
          onChange={handleTimeChange}
          value={currentValue}
          name={props.name}
          onKeyDown={(e) => {
            if (e.key === 'Enter' && props.onEnter) {
              props.onEnter();
            }
          }}
          type="time"
          step={props.step || 60} // Default step 60 detik (1 menit)
          placeholder={props.placeholder || 'HH:MM'}
          lang="en-GB" // Force British English locale which uses 24-hour format
          className={cn(
            props.startIcon ? 'pl-12' : 'pl-10', // Space untuk default clock icon
            props.endIcon ? 'pr-9' : '',
            errorMessage ? ' outline-red-500 border-red-500 bg-red-100' : '',
            // Additional class for 24-hour format styling
            '[&::-webkit-calendar-picker-indicator]:opacity-100',
          )}
          id={props.id}
          style={{
            // Additional inline styles to ensure 24-hour format
            colorScheme: 'light dark',
          }}
        />

        {/* Default clock icon jika tidak ada startIcon */}
        {!props.startIcon && <Clock className="absolute left-3 h-4 w-4 text-gray-500" />}

        {props.endIcon && (
          <span className="absolute text-gray-500 right-3 flex items-center pl-3">{props.endIcon}</span>
        )}
      </div>

      {(errorMessage || props.helperText) && (
        <p className={cn('text-xs mt-1', errorMessage ? 'text-red-500' : 'text-gray-500')}>
          {errorMessage || props.helperText}
        </p>
      )}
    </div>
  );
}
