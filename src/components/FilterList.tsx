import { ListFilter } from 'lucide-react';
import { MdClose } from 'react-icons/md';
import { Button } from './ui/button';
import {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerDescription,
  DrawerFooter,
  DrawerHeader,
  Drawer<PERSON><PERSON>le,
  DrawerTrigger,
} from './ui/drawer';
import { Separator } from './ui/separator';

interface Iprops {
  title?: string;
  description?: string;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  onReset?: () => void;
  onSubmit?: () => void;
  children?: React.ReactNode;
}

export default function FilterList(props: Iprops) {
  return (
    <Drawer direction="right" open={props.open} onOpenChange={props.onOpenChange}>
      <DrawerTrigger asChild>
        <Button variant="outline">
          <ListFilter className="mr-2" />
          FILTER
        </Button>
      </DrawerTrigger>
      <DrawerContent>
        <DrawerHeader>
          <div className="flex items-center justify-between">
            <DrawerTitle>{props.title ?? 'Filter'}</DrawerTitle>
            <DrawerClose asChild>
              <Button size="icon" variant="ghost">
                <MdClose />
              </Button>
            </DrawerClose>
          </div>
          <DrawerDescription>{props.description ?? 'Pilih kriteria untuk menyaring data'}</DrawerDescription>
        </DrawerHeader>
        <Separator />
        <div className="p-4">{props.children}</div> {/* Area untuk isian filter */}
        <DrawerFooter>
          <Button onClick={props.onSubmit}>Submit</Button>
          <DrawerClose asChild>
            <Button variant="outline" onClick={props.onReset}>
              Cancel
            </Button>
          </DrawerClose>
        </DrawerFooter>
      </DrawerContent>
    </Drawer>
  );
}
