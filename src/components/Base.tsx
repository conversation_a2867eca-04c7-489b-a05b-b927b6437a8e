import type { PageType } from '@/enums/page-type';
import { AnimatePresence } from 'framer-motion';
import type { ReactNode } from 'react';
import AdminPanelLayout from './admin-panel/admin-panel-layout';
import TopBar from './TopBar';

export default function Base(props: IProps) {
  function render() {
    switch (props.type) {
      case 'PRIMARY':
        return (
          <AdminPanelLayout>
            <TopBar />
            <>{props.children}</>
          </AdminPanelLayout>
        );
      case 'FULL_PAGE':
        return (
          <AnimatePresence mode="wait">
            <>{props.children}</>
          </AnimatePresence>
        );
      case 'DASHBOARD':
        return (
          <div className={'w-full'}>
            {/* <TopBar /> */}
            <div className={'flex w-full'}>
              {/* <AppSidebar /> */}
              <div className={'flex-1 '}>{props.children}</div>
            </div>
          </div>
        );
      default:
        return <>{props.children}</>;
    }
  }

  return <>{render()}</>;
}

interface IProps {
  type: PageType;
  children: ReactNode;
}
