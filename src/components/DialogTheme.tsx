import { LOCAL_STORAGE_KEY } from '@/constants/local-storage-key';
import { AlertDialogDescription } from '@radix-ui/react-alert-dialog';
import { useState } from 'react';
import { MdDarkMode, MdLightMode } from 'react-icons/md';
import { useTheme } from './providers/theme-provider';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogContent,
  AlertDialogFooter,
  AlertDialogTitle,
} from './ui/alert-dialog';
import { Button } from './ui/button';

export default function DialogTheme() {
  const { setTheme, theme } = useTheme();
  const data = !localStorage.getItem(LOCAL_STORAGE_KEY?.THEME_VIEW);
  const [open, setOpen] = useState<boolean>(data);

  function onSetTheme() {
    setOpen(false);
    localStorage.setItem(LOCAL_STORAGE_KEY.THEME_VIEW, '1');
  }

  return (
    <AlertDialog open={open}>
      <AlertDialogContent>
        <AlertDialogTitle className="text-center"><PERSON><PERSON> pilih tema dulu, kamu tim yang mana ?</AlertDialogTitle>
        <div className="flex items-center text-2xl justify-center gap-6">
          <div className="flex items-center gap-2">
            <Button variant={'ghost'} onClick={() => setTheme('dark')}>
              <MdDarkMode />
              <div>Sigelap</div>
            </Button>
          </div>
          <div
            onClick={() => {
              if (theme === 'light') {
                setTheme('dark');
              } else {
                setTheme('light');
              }
            }}
          >
            <div className="text-xs">|</div>
          </div>
          <div className="flex items-center gap-2" onClick={() => setTheme('light')}>
            <Button variant={'ghost'} onClick={() => setTheme('light')}>
              <div>Siterang</div> <MdLightMode />
            </Button>
          </div>
        </div>
        <AlertDialogDescription className="text-center">
          <span className="text-center"> Pilih tema sesuai gaya kamu. Tenang, kamu bisa ganti kapan aja!</span>
        </AlertDialogDescription>
        <AlertDialogFooter className="grid grid-cols-1">
          <AlertDialogAction onClick={onSetTheme}>Terapkan Tema</AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
