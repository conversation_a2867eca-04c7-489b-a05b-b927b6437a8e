import { Menu } from '@/components/admin-panel/menu';
import { SidebarToggle } from '@/components/admin-panel/sidebar-toggle';
import { ASSETS } from '@/constants/assets';
import { useSidebar } from '@/hooks/use-sidebar';
import { useStore } from '@/hooks/use-store';
import { cn } from '@/lib/utils';
import { Link } from 'react-router-dom';
import { ROUTES } from '@/routes/routes.ts';

export function Sidebar() {
  const sidebar = useStore(useSidebar, (x) => x);
  if (!sidebar) return null;
  const { isOpen, toggleOpen, getOpenState, setIsHover, settings } = sidebar;
  return (
    <aside
      className={cn(
        'fixed top-0 left-0 z-20 h-screen bg-white -translate-x-full lg:translate-x-0 transition-[width] ease-in-out duration-300',
        !getOpenState() ? 'w-[90px]' : 'w-72',
        settings.disabled && 'hidden',
      )}
      style={{ zIndex: 2 }}
    >
      <SidebarToggle isOpen={isOpen} setIsOpen={toggleOpen} />
      <div
        onMouseEnter={() => setIsHover(true)}
        onMouseLeave={() => setIsHover(false)}
        className="relative h-full flex flex-col px-3 py-4 shadow-md dark:shadow-zinc-800"
      >
        <div
          className={cn(
            'transition-transform text-start ease-in-out duration-300 mb-1 ',
            !getOpenState() ? 'translate-x-5' : 'translate-x-2',
          )}
        >
          <Link to={ROUTES.HOME()} className="flex items-center gap-2">
            <img src={ASSETS.LG_BRAND} alt="nuca lale" className="w-6 h-6 mr-1" />
            <div>
              <h1
                className={cn(
                  'font-bold text-lg whitespace-nowrap transition-[transform,opacity,display] ease-in-out duration-300',
                  !getOpenState() ? '-translate-x-96 opacity-0 hidden' : 'translate-x-0 opacity-100',
                )}
              >
                Nuca Lale
              </h1>
              {getOpenState() && <p className={'text-xs text-gray-500'}>Pemerintah Kab. Manggarai</p>}
            </div>
          </Link>
        </div>
        <Menu isOpen={getOpenState()} />
      </div>
    </aside>
  );
}
