import type { ILabelValue } from '@/types/type/ILabelValue.ts';
import { createSlice, type PayloadAction } from '@reduxjs/toolkit';

export interface IMasterDataReducers {
  listReligion: ILabelValue<string>[];
  listMaritalStatus: ILabelValue<string>[];
}

const initState: IMasterDataReducers = {
  listMaritalStatus: [],
  listReligion: [],
};
export const MasterDataSlice = createSlice({
  name: 'master-data',
  initialState: initState,
  reducers: {
    listReligion: (state: IMasterDataReducers, action: PayloadAction<ILabelValue<string>[]>) => {
      state.listReligion = action.payload;
    },
    listMaritalStatus: (state: IMasterDataReducers, action: PayloadAction<ILabelValue<string>[]>) => {
      state.listMaritalStatus = action.payload;
    },
  },
});
