import { clsx, type ClassValue } from 'clsx';
import { twMerge } from 'tailwind-merge';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export const getInitials = (name: string) => {
  return name
    .split(' ')
    .map((word) => word.charAt(0))
    .join('')
    .toUpperCase()
    .slice(0, 2);
};

import { format, parse } from 'date-fns';

export function normalizeTime(value: string): string {
  try {
    // Coba parse dengan format HH:mm:ss
    const parsed = parse(value, 'HH:mm:ss', new Date());
    return format(parsed, 'HH:mm');
  } catch {
    try {
      // Coba parse dengan format HH:mm
      const parsed = parse(value, 'HH:mm', new Date());
      return format(parsed, 'HH:mm');
    } catch {
      return ''; // fallback untuk invalid input
    }
  }
}
