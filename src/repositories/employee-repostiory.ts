import { ENDPOINT } from '@/constants/endpoint';
import ErrorService from '@/services/error.service';
import { HttpService } from '@/services/http.service';
import type { IResDetailEmployee } from '@/types/response/IResDetailEmployee';
import type { IResListEmployee } from '@/types/response/IResListEmployee';
import type { BaseResponse, BaseResponsePaginated } from '@/types/response/IResModel';
import type { IFilterList } from '@/types/type/IFilterList';
import { buildQueryString, buildSearchParams } from '@/utils/search-params.utils';

export class EmployeeRepository {
  httpService = new HttpService();
  errorService = new ErrorService();

  async setActiveEmployee(id: string) {
    try {
      const res: BaseResponse<any> = await this.httpService.PATCH(ENDPOINT.SET_ACTIVE_EMPLOYEE(id));
      return res.data;
    } catch (e) {
      this.errorService.fetchApiError(e);
    }
  }

  async getDetailEmployee(id?: string): Promise<IResDetailEmployee> {
    try {
      const res: BaseResponse<IResDetailEmployee> = await this.httpService.GET(ENDPOINT.DETAIL_EMPLOYEE(id || ''));
      return res.data.response_data;
    } catch (e) {
      this.errorService.fetchApiError(e);
      throw e;
    }
  }

  async listEmployee(filterData: IFilterList, setPage: (e: number) => void, setTotal: (e: number) => void) {
    const params = buildSearchParams(filterData);
    const url = `${ENDPOINT.LIST_EMPLOYEE()}` + buildQueryString(params);
    return this.httpService
      .GET(url)
      .then((res: BaseResponsePaginated<IResListEmployee[]>) => {
        setPage(res.data.paginated_data.page_count || 0);
        setTotal(res.data.paginated_data.total_data || 0);
        return res.data.response_data;
      })
      .catch((e) => {
        this.errorService.fetchApiError(e);
        return [];
      });
  }
}
