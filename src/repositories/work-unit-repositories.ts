import { ENDPOINT } from '@/constants/endpoint';
import ErrorService from '@/services/error.service';
import { HttpService } from '@/services/http.service';
import type { IReqCreateWorkUnit } from '@/types/request/IReqCreateWorkUnit';
import type { IReqScheduleRegular } from '@/types/request/IReqScheduleRegular.ts';
import type { IReqSettingLocation } from '@/types/request/IReqSettingLocation.ts';
import type { IResDetailWorkUnit } from '@/types/response/IResDetailWorkUnit';
import type { IResListShift } from '@/types/response/IResListShift';
import type { IResWorkUnit } from '@/types/response/IResListWorkUnit';
import type { IResLocationWorkUnit } from '@/types/response/IResLocationWorkUnit.ts';
import type { BaseResponse, BaseResponseMessage, BaseResponsePaginated } from '@/types/response/IResModel';
import type { IResScheduleRegularWorkUnit } from '@/types/response/IResScheduleRegularWorkUnit.ts';
import type { IFilterList } from '@/types/type/IFilterList';
import { buildQueryString, buildSearchParams } from '@/utils/search-params.utils';
import type { IReqSettingShiftWorkUnit } from '@/types/request/IReqSettingShiftWorkUnit.ts';
import type { IResListScheduleShift } from '@/types/response/IResListScheduleShift.ts';
import type { IReqManageShift } from '@/types/request/IReqManageShift';

export class WorkUnitRepository {
  private httpService = new HttpService();
  private errorService = new ErrorService();

  async getDetailWorkUnit(id: string) {
    return this.httpService
      .GET(ENDPOINT.DETAIL_WORK_UNIT(id))
      .then((res: BaseResponse<IResDetailWorkUnit>) => {
        return res.data.response_data;
      })
      .catch((e) => {
        this.errorService.fetchApiError(e);
        throw e;
      });
  }

  async getLocationWorkUnit(id: string) {
    return this.httpService
      .GET(ENDPOINT.LIST_LOCATION_WORK_UNI(id))
      .then((res: BaseResponse<IResLocationWorkUnit[]>) => {
        return res.data.response_data;
      })
      .catch((e) => {
        this.errorService.fetchApiError(e);
        throw e;
      });
  }

  async listWorkUnit(filterData: IFilterList) {
    const params = buildSearchParams(filterData);
    return this.httpService
      .GET(ENDPOINT.LIST_WORK_UNIT() + buildQueryString(params))
      .then((res: BaseResponsePaginated<IResWorkUnit[]>) => {
        return res.data;
      })
      .catch((e) => {
        this.errorService.fetchApiError(e);
        throw e;
      });
  }

  async createWorkUnit(data: IReqCreateWorkUnit) {
    return this.httpService
      .POST(ENDPOINT.CREATE_WORK_UNIT(), data)
      .then((res: BaseResponse<IResWorkUnit>) => {
        return res.data.response_data;
      })
      .catch((e) => {
        this.errorService.fetchApiError(e);
        throw e;
      });
  }

  async editWorkUnit(id: string, data: IReqCreateWorkUnit) {
    return this.httpService
      .PUT(ENDPOINT.EDIT_WORK_UNIT(id), data)
      .then((res: BaseResponse<IResWorkUnit>) => {
        return res.data.response_data;
      })
      .catch((e) => {
        this.errorService.fetchApiError(e);
        throw e;
      });
  }

  async submitSettingLocation(id: string | undefined, e: IReqSettingLocation[]) {
    try {
      const res = await this.httpService.POST(ENDPOINT.SETTING_WORK_UNIT_LOCATION(id || ''), e);
      return res.data.response_data;
    } catch (e_1) {
      this.errorService.fetchApiError(e_1);
      throw e_1;
    }
  }

  async getScheduleRegularWorkUnit(id: string) {
    try {
      const res: BaseResponse<IResScheduleRegularWorkUnit[]> = await this.httpService.GET(
        ENDPOINT.LIST_SCHEDULE_REGULAR_WORK_UNIT(id),
      );
      return res.data.response_data;
    } catch (e) {
      this.errorService.fetchApiError(e);
      throw e;
    }
  }

  async settingScheduleRegular(id: string | undefined, e: IReqScheduleRegular[]) {
    try {
      const res: BaseResponse<IResScheduleRegularWorkUnit[]> = await this.httpService.POST(
        ENDPOINT.SETTING_SCHEDULE_REGULAR_WORK_UNIT(id || ''),
        e,
      );
      return res.data.response_data;
    } catch (e) {
      this.errorService.fetchApiError(e);
      throw e;
    }
  }

  async getListShift(id: string) {
    try {
      const res: BaseResponse<IResListShift[]> = await this.httpService.GET(
        ENDPOINT.LIST_SHIFT_BY_WORK_UNIT_ID(id || ''),
      );
      return res.data.response_data || [];
    } catch (e) {
      this.errorService.fetchApiError(e);
      throw e;
    }
  }

  async settingShiftWorkUnit(id: string, data: IReqSettingShiftWorkUnit[]) {
    try {
      const res: BaseResponse<IResListShift[]> = await this.httpService.POST(
        ENDPOINT.SETTING_SHIFT_WORK_UNIT(id || ''),
        data,
      );
      return res.data.response_data || [];
    } catch (e) {
      this.errorService.fetchApiError(e);
    }
  }

  async deleteShift(id: string) {
    try {
      const res: BaseResponseMessage = await this.httpService.DELETE(ENDPOINT.SETTING_SHIFT_WORK_UNIT(id || ''));
      return res.data.message;
    } catch (e) {
      this.errorService.fetchApiError(e);
    }
  }

  async listScheduleShift(id: string, date: { start: string; end: string }) {
    try {
      const res: BaseResponse<IResListScheduleShift[]> = await this.httpService.GET(
        ENDPOINT.LIST_SCHEDULE_SHIFT_WORK_UNIT_BY_DATE(id, date),
      );
      return res.data.response_data;
    } catch (e) {
      this.errorService.fetchApiError(e);
      return [];
    }
  }
  async manageShift(id: string, data: IReqManageShift[]) {
    try {
      const res: BaseResponseMessage = await this.httpService.POST(ENDPOINT.MANAGE_SHIFT(id), data);
      return res.data.message;
    } catch (e) {
      this.errorService.fetchApiError(e);
      return [];
    }
  }
}
